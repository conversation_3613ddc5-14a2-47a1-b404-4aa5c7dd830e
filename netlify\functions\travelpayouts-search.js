/**
 * Travelpayouts Flight Search API
 *
 * This Netlify function handles flight search requests using the Travelpayouts (Aviasales) API.
 * It implements two methods:
 * 1. POST - Initialize a search and get a search_id
 * 2. GET - Poll for results using a search_id
 *
 * Documentation: https://support.travelpayouts.com/hc/en-us/articles/*********-Aviasales-Flights-Search-API-real-time-and-multi-city-search
 */

const axios = require('axios');
const crypto = require('crypto');

// Headers for CORS and content type
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Content-Type': 'application/json'
};

// Travelpayouts API endpoints
const TRAVELPAYOUTS_SEARCH_URL = 'https://api.travelpayouts.com/v1/flight_search';
const TRAVELPAYOUTS_RESULTS_URL = 'https://api.travelpayouts.com/v1/flight_search_results';

// Environment variables
const TRAVELPAYOUTS_MARKER = process.env.TRAVELPAYOUTS_MARKER || '627715';
const TRAVELPAYOUTS_TOKEN = process.env.TRAVELPAYOUTS_TOKEN || '********************************';

/**
 * Create an MD5 signature for the Travelpayouts API
 * @param {Object} params - Search parameters
 * @returns {string} MD5 signature
 */
function createSignature(params) {
  // Create a string with all parameters in alphabetical order
  let signatureString = TRAVELPAYOUTS_TOKEN;

  // Add marker
  signatureString += ':' + TRAVELPAYOUTS_MARKER;

  // Extract passenger info
  const adults = params.passengers?.adults || 1;
  const children = params.passengers?.children || 0;
  const infants = params.passengers?.infants || 0;

  // Extract segments info
  const segments = params.segments || [];

  // Build a custom signature string based on the specific parameters
  // This is a simplified approach - in a production environment, you would
  // need to extract and sort all parameters according to the API documentation

  if (segments.length === 2) {
    // Round trip
    signatureString += ':' + adults;
    signatureString += ':' + params.user_ip;
    signatureString += ':' + segments[0].origin;
    signatureString += ':' + segments[0].destination;
    signatureString += ':' + segments[1].origin;
    signatureString += ':' + segments[1].destination;
    signatureString += ':' + params.trip_class;
    signatureString += ':' + params.locale;
    signatureString += ':' + params.host;
    signatureString += ':' + segments[0].date;
    signatureString += ':' + segments[1].date;
    signatureString += ':' + children;
    signatureString += ':' + infants;
  } else if (segments.length === 1) {
    // One way
    signatureString += ':' + adults;
    signatureString += ':' + params.user_ip;
    signatureString += ':' + segments[0].origin;
    signatureString += ':' + segments[0].destination;
    signatureString += ':' + params.trip_class;
    signatureString += ':' + params.locale;
    signatureString += ':' + params.host;
    signatureString += ':' + segments[0].date;
    signatureString += ':' + children;
    signatureString += ':' + infants;
  }

  console.log('Signature string:', signatureString);

  // Create MD5 hash
  return crypto.createHash('md5').update(signatureString).digest('hex');
}

/**
 * Initialize a flight search
 * @param {Object} params - Search parameters
 * @returns {Promise<Object>} Search response with search_id
 */
async function initializeSearch(params) {
  try {
    console.log('Initializing flight search with parameters:', params);

    // Prepare segments
    const segments = [];

    // For one-way or round trip
    if (params.origin && params.destination && params.departureDate) {
      segments.push({
        origin: params.origin,
        destination: params.destination,
        date: params.departureDate
      });

      // Add return segment for round trip
      if (params.returnDate) {
        segments.push({
          origin: params.destination,
          destination: params.origin,
          date: params.returnDate
        });
      }
    }

    // Prepare request body
    const requestBody = {
      marker: TRAVELPAYOUTS_MARKER,
      host: 'vestigia.app',
      user_ip: params.ip || '127.0.0.1',
      locale: params.locale || 'en',
      trip_class: params.tripClass || 'Y',
      passengers: {
        adults: parseInt(params.adults || 1, 10),
        children: parseInt(params.children || 0, 10),
        infants: parseInt(params.infants || 0, 10)
      },
      segments: segments
    };

    // Create signature
    requestBody.signature = createSignature(requestBody);

    console.log('Request body:', JSON.stringify(requestBody));

    // Make the API request
    const response = await axios.post(TRAVELPAYOUTS_SEARCH_URL, requestBody, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Search initialized with ID:', response.data.search_id);

    return response.data;
  } catch (error) {
    console.error('Error initializing search:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Poll for search results
 * @param {string} searchId - Search ID
 * @returns {Promise<Object>} Search results
 */
async function pollResults(searchId) {
  try {
    console.log(`Polling for results with search ID: ${searchId}`);

    // Make the API request
    const response = await axios.get(`${TRAVELPAYOUTS_RESULTS_URL}?uuid=${searchId}`, {
      headers: {
        'Accept-Encoding': 'gzip,deflate,sdch'
      }
    });

    // Check if we have only the search_id in the response
    if (response.data.length === 1 && response.data[0].search_id) {
      console.log('Search still in progress');
      return { status: 'searching', search_id: searchId };
    }

    // Process the results
    console.log('Search complete, processing results');

    // Extract flight data, currency, and other information
    const flightData = response.data[0] || {};
    const proposals = flightData.proposals || [];

    return {
      status: 'complete',
      search_id: searchId,
      data: proposals,
      currency: flightData.currency,
      currency_rates: flightData.currency_rates,
      airlines: flightData.airlines,
      airports: flightData.airports,
      gates_info: flightData.gates_info
    };
  } catch (error) {
    console.error('Error polling for results:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Netlify function handler
 */
exports.handler = async (event, context) => {
  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'Preflight call successful' })
    };
  }

  try {
    // Handle GET request (poll for results)
    if (event.httpMethod === 'GET') {
      const params = event.queryStringParameters || {};
      const { search_id } = params;

      if (!search_id) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Missing required parameter: search_id' })
        };
      }

      const results = await pollResults(search_id);

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify(results)
      };
    }

    // Handle POST request (initialize search)
    if (event.httpMethod === 'POST') {
      const params = JSON.parse(event.body || '{}');

      // Validate required parameters
      if (!params.origin || !params.destination || !params.departureDate) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({
            error: 'Missing required parameters. Please provide origin, destination, and departureDate.'
          })
        };
      }

      const searchData = await initializeSearch(params);

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          search_id: searchData.search_id,
          message: 'Search initialized successfully'
        })
      };
    }

    // Handle unsupported methods
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  } catch (error) {
    console.error('Error in travelpayouts-search function:', error);

    return {
      statusCode: error.response?.status || 500,
      headers,
      body: JSON.stringify({
        error: error.response?.data?.message || 'Internal server error',
        message: error.message
      })
    };
  }
};
