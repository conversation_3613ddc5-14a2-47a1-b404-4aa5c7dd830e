/**
 * Amadeus API Client
 * 
 * This client provides methods to interact with the Amadeus Flight Search API.
 * Documentation: https://developers.amadeus.com/
 */

class AmadeusAPI {
    constructor() {
        this.clientId = 'YOUR_AMADEUS_CLIENT_ID'; // Replace with your client ID
        this.clientSecret = 'YOUR_AMADEUS_CLIENT_SECRET'; // Replace with your client secret
        this.baseUrl = 'https://test.api.amadeus.com/v2'; // Use test URL for development
        this.accessToken = null;
        this.tokenExpiry = null;
    }

    /**
     * Get access token for API authentication
     * @returns {Promise<string>} Access token
     */
    async getAccessToken() {
        // Return existing token if it's still valid
        if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
            return this.accessToken;
        }

        try {
            const response = await fetch('https://test.api.amadeus.com/v1/security/oauth2/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: new URLSearchParams({
                    grant_type: 'client_credentials',
                    client_id: this.clientId,
                    client_secret: this.clientSecret
                })
            });

            if (!response.ok) {
                throw new Error('Failed to get access token');
            }

            const data = await response.json();
            this.accessToken = data.access_token;
            this.tokenExpiry = Date.now() + (data.expires_in * 1000);
            return this.accessToken;
        } catch (error) {
            console.error('Error getting access token:', error);
            throw error;
        }
    }

    /**
     * Search for flights using Amadeus API
     * @param {Object} params - Search parameters
     * @param {Function} progressCallback - Callback for search progress updates
     * @returns {Promise<Object>} Flight search results
     */
    async searchFlights(params, progressCallback) {
        try {
            if (progressCallback) {
                progressCallback({ status: 'searching', message: 'Searching for flights...', progress: 50 });
            }

            // Get access token
            const token = await this.getAccessToken();

            // Format search parameters
            const searchParams = this.formatSearchParams(params);

            // Make the API request
            const response = await fetch(`${this.baseUrl}/shopping/flight-offers`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(searchParams)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.errors?.[0]?.detail || 'Failed to search flights');
            }

            const data = await response.json();

            if (progressCallback) {
                progressCallback({ status: 'complete', message: `Found ${data.data.length} flights`, progress: 100 });
            }

            return {
                status: 'complete',
                data: data.data,
                currency: data.meta?.currency || 'USD',
                currency_rates: data.meta?.currency_rates || {}
            };
        } catch (error) {
            console.error('Error searching flights:', error);

            if (progressCallback) {
                progressCallback({ status: 'error', message: error.message, progress: 0 });
            }

            throw error;
        }
    }

    /**
     * Format search parameters according to Amadeus API requirements
     * @param {Object} params - Raw search parameters
     * @returns {Object} Formatted parameters
     */
    formatSearchParams(params) {
        // Extract IATA codes from full airport names
        const extractIATACode = (airportString) => {
            const match = airportString.match(/^([A-Z]{3})/);
            return match ? match[1] : null;
        };

        const originCode = extractIATACode(params.origin);
        const destinationCode = extractIATACode(params.destination);

        if (!originCode || !destinationCode) {
            throw new Error('Invalid airport codes');
        }

        return {
            originLocationCode: originCode,
            destinationLocationCode: destinationCode,
            departureDate: params.departureDate,
            returnDate: params.returnDate,
            adults: params.adults || 1,
            children: params.children || 0,
            infants: params.infants || 0,
            currencyCode: 'USD',
            max: 20
        };
    }
}

// Create a global amadeusAPI object
window.amadeusAPI = new AmadeusAPI(); 