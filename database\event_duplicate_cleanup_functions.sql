-- Event Duplicate Cleanup Functions for Vestigia
-- These functions help identify and remove duplicate events based on user ID and event content
-- Run this directly in the Supabase SQL Editor

-- Function to find duplicate events for a user
CREATE OR REPLACE FUNCTION find_duplicate_events(p_user_id TEXT)
RETURNS TABLE (
  id INTEGER,
  event_id TEXT,
  title TEXT,
  start_time TIMESTAMPTZ,
  end_time TIMESTAMPTZ,
  location TEXT,
  created_at TIMESTAMPTZ,
  duplicate_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  WITH duplicates AS (
    SELECT 
      ue.id,
      ue.event_id,
      ue.title,
      ue.start_time,
      ue.end_time,
      ue.location,
      ue.created_at,
      ROW_NUMBER() OVER (
        PARTITION BY 
          ue.user_id,
          TRIM(LOWER(ue.title)),
          ue.start_time,
          COALESCE(ue.end_time, ue.start_time),
          TRIM(LOWER(COALESCE(ue.location, ''))),
          TRIM(LOWER(COALESCE(ue.description, ''))),
          TRIM(LOWER(COALESCE(ue.category, '')))
        ORDER BY ue.updated_at DESC, ue.created_at DESC, ue.id DESC
      ) as rn,
      COUNT(*) OVER (
        PARTITION BY 
          ue.user_id,
          TRIM(LOWER(ue.title)),
          ue.start_time,
          COALESCE(ue.end_time, ue.start_time),
          TRIM(LOWER(COALESCE(ue.location, ''))),
          TRIM(LOWER(COALESCE(ue.description, ''))),
          TRIM(LOWER(COALESCE(ue.category, '')))
      ) as count
    FROM user_events ue
    WHERE ue.user_id = p_user_id
  )
  SELECT 
    d.id,
    d.event_id,
    d.title,
    d.start_time,
    d.end_time,
    d.location,
    d.created_at,
    d.count - 1 as duplicate_count
  FROM duplicates d
  WHERE d.count > 1
  ORDER BY d.count DESC, d.created_at DESC;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION find_duplicate_events IS 'Finds duplicate events for a user based on title, start/end time, location, description, and category';

-- Function to delete duplicate events, keeping the most recent one
CREATE OR REPLACE FUNCTION delete_duplicate_events(p_user_id TEXT)
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER := 0;
BEGIN
  WITH duplicates_to_delete AS (
    SELECT id
    FROM (
      SELECT 
        id,
        ROW_NUMBER() OVER (
          PARTITION BY 
            user_id,
            TRIM(LOWER(title)),
            start_time,
            COALESCE(end_time, start_time),
            TRIM(LOWER(COALESCE(location, ''))),
            TRIM(LOWER(COALESCE(description, ''))),
            TRIM(LOWER(COALESCE(category, '')))
          ORDER BY updated_at DESC, created_at DESC, id DESC
        ) as rn
      FROM user_events
      WHERE user_id = p_user_id
    ) t
    WHERE rn > 1
  )
  DELETE FROM user_events
  WHERE id IN (SELECT id FROM duplicates_to_delete);
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION delete_duplicate_events IS 'Deletes duplicate events for a user, keeping the most recent one based on updated_at timestamp';

-- Function to clean up all duplicate events for all users (admin only)
CREATE OR REPLACE FUNCTION cleanup_all_duplicate_events()
RETURNS JSONB AS $$
DECLARE
  user_record RECORD;
  total_deleted INTEGER := 0;
  result JSONB := '[]'::JSONB;
  user_result JSONB;
  user_deleted INTEGER;
BEGIN
  -- Process each user's events separately
  FOR user_record IN SELECT DISTINCT user_id FROM user_events LOOP
    -- Delete duplicates for this user
    SELECT delete_duplicate_events(user_record.user_id) INTO user_deleted;
    
    -- Build result object for this user
    SELECT jsonb_build_object(
      'user_id', user_record.user_id,
      'deleted', user_deleted
    ) INTO user_result;
    
    -- Add to results array
    result := result || user_result::jsonb;
    total_deleted := total_deleted + user_deleted;
  END LOOP;
  
  RETURN jsonb_build_object(
    'total_duplicates_removed', total_deleted,
    'by_user', result
  );
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION cleanup_all_duplicate_events IS 'Cleans up duplicate events for all users and returns summary statistics';

-- Function to get duplicate statistics for a user
CREATE OR REPLACE FUNCTION get_duplicate_event_stats(p_user_id TEXT)
RETURNS JSONB AS $$
DECLARE
  total_events INTEGER;
  duplicate_groups INTEGER;
  total_duplicates INTEGER;
BEGIN
  -- Get total events for user
  SELECT COUNT(*) INTO total_events
  FROM user_events
  WHERE user_id = p_user_id;
  
  -- Get duplicate statistics
  WITH duplicate_analysis AS (
    SELECT 
      COUNT(*) as group_size,
      COUNT(*) - 1 as duplicates_in_group
    FROM user_events
    WHERE user_id = p_user_id
    GROUP BY 
      user_id,
      TRIM(LOWER(title)),
      start_time,
      COALESCE(end_time, start_time),
      TRIM(LOWER(COALESCE(location, ''))),
      TRIM(LOWER(COALESCE(description, ''))),
      TRIM(LOWER(COALESCE(category, '')))
    HAVING COUNT(*) > 1
  )
  SELECT 
    COUNT(*) as groups,
    COALESCE(SUM(duplicates_in_group), 0) as duplicates
  INTO duplicate_groups, total_duplicates
  FROM duplicate_analysis;
  
  RETURN jsonb_build_object(
    'user_id', p_user_id,
    'total_events', total_events,
    'duplicate_groups', COALESCE(duplicate_groups, 0),
    'total_duplicates', COALESCE(total_duplicates, 0),
    'unique_events', total_events - COALESCE(total_duplicates, 0)
  );
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION get_duplicate_event_stats IS 'Returns statistics about duplicate events for a user';

-- Test function to verify the duplicate detection logic
CREATE OR REPLACE FUNCTION test_event_duplicate_detection()
RETURNS TEXT AS $$
BEGIN
  -- This function can be used to test the duplicate detection logic
  -- It will return information about how the functions work
  RETURN 'Event duplicate detection functions created successfully. 
  
Functions available:
- find_duplicate_events(user_id): Find duplicates for a user
- delete_duplicate_events(user_id): Remove duplicates for a user  
- cleanup_all_duplicate_events(): Clean all users duplicates
- get_duplicate_event_stats(user_id): Get duplicate statistics

Duplicate detection criteria:
- Same user_id
- Same title (case-insensitive, trimmed)
- Same start_time
- Same end_time (or start_time if end_time is null)
- Same location (case-insensitive, trimmed, empty string if null)
- Same description (case-insensitive, trimmed, empty string if null)
- Same category (case-insensitive, trimmed, empty string if null)

When duplicates are found, the most recent event is kept based on:
1. updated_at timestamp (most recent first)
2. created_at timestamp (most recent first)  
3. id (highest first)';
END;
$$ LANGUAGE plpgsql;
