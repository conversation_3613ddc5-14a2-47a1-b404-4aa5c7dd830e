<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Cleanup Test - Vestigia</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .button.danger:hover {
            background-color: #c82333;
        }
        .results {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .input-group {
            margin-bottom: 15px;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>Event Cleanup Test Page</h1>
    <p>This page allows you to test the event deduplication and cleanup functions.</p>

    <div class="container">
        <h2>User Configuration</h2>
        <div class="input-group">
            <label for="userId">User ID:</label>
            <input type="text" id="userId" placeholder="Enter user ID for testing" value="test-user-123">
        </div>
    </div>

    <div class="container">
        <h2>Local Event Cleanup</h2>
        <p>Test local event deduplication functions:</p>
        <button class="button" onclick="testLocalDeduplication()">Test Local Deduplication</button>
        <button class="button" onclick="createTestEvents()">Create Test Events</button>
        <button class="button" onclick="showLocalEvents()">Show Local Events</button>
        <div id="localResults" class="results" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>Remote Event Cleanup</h2>
        <p>Test Supabase event cleanup functions:</p>
        <button class="button" onclick="findDuplicates()">Find Duplicates</button>
        <button class="button" onclick="getStats()">Get Statistics</button>
        <button class="button danger" onclick="cleanupDuplicates()">Cleanup Duplicates</button>
        <div id="remoteResults" class="results" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>Comprehensive Cleanup</h2>
        <p>Run complete cleanup for both local and remote events:</p>
        <button class="button" onclick="runComprehensiveCleanup()">Run Comprehensive Cleanup</button>
        <div id="comprehensiveResults" class="results" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>Statistics Dashboard</h2>
        <div id="statsContainer" class="stats">
            <div class="stat-card">
                <div class="stat-number" id="localEventCount">-</div>
                <div class="stat-label">Local Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="remoteEventCount">-</div>
                <div class="stat-label">Remote Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="duplicateCount">-</div>
                <div class="stat-label">Duplicates Found</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="cleanupCount">-</div>
                <div class="stat-label">Last Cleanup</div>
            </div>
        </div>
        <button class="button" onclick="updateStats()">Refresh Statistics</button>
    </div>

    <!-- Include required scripts -->
    <script src="js/itinerary-api-client.js"></script>
    <script src="js/event-cleanup-utils.js"></script>

    <script>
        // Test functions
        function getUserId() {
            return document.getElementById('userId').value || 'test-user-123';
        }

        function showResults(containerId, data) {
            const container = document.getElementById(containerId);
            container.style.display = 'block';
            container.textContent = JSON.stringify(data, null, 2);
        }

        function createTestEvents() {
            const userId = getUserId();
            const testEvents = [
                {
                    id: 'test-1',
                    user_id: userId,
                    title: 'Meeting with Client',
                    start: '2024-01-15T10:00:00Z',
                    end: '2024-01-15T11:00:00Z',
                    description: 'Important client meeting',
                    location: 'Office',
                    category: 'work',
                    updated_at: new Date().toISOString()
                },
                {
                    id: 'test-2',
                    user_id: userId,
                    title: 'Meeting with Client',
                    start: '2024-01-15T10:00:00Z',
                    end: '2024-01-15T11:00:00Z',
                    description: 'Important client meeting',
                    location: 'Office',
                    category: 'work',
                    updated_at: new Date(Date.now() - 1000).toISOString()
                },
                {
                    id: 'test-3',
                    user_id: userId,
                    title: 'Lunch Break',
                    start: '2024-01-15T12:00:00Z',
                    end: '2024-01-15T13:00:00Z',
                    description: 'Lunch with team',
                    location: 'Restaurant',
                    category: 'personal',
                    updated_at: new Date().toISOString()
                }
            ];

            localStorage.setItem('itineraryEvents', JSON.stringify(testEvents));
            showResults('localResults', { message: 'Test events created', events: testEvents });
            updateStats();
        }

        function testLocalDeduplication() {
            const events = JSON.parse(localStorage.getItem('itineraryEvents') || '[]');
            const userId = getUserId();
            
            if (window.eventCleanupUtils) {
                const deduplicated = window.eventCleanupUtils.deduplicateEventsLocally(events, userId);
                showResults('localResults', {
                    original_count: events.length,
                    deduplicated_count: deduplicated.length,
                    removed: events.length - deduplicated.length,
                    events: deduplicated
                });
                updateStats();
            } else {
                showResults('localResults', { error: 'Event cleanup utils not loaded' });
            }
        }

        function showLocalEvents() {
            const events = JSON.parse(localStorage.getItem('itineraryEvents') || '[]');
            showResults('localResults', { local_events: events });
        }

        async function findDuplicates() {
            try {
                const userId = getUserId();
                const result = await window.itineraryAPI.findDuplicateEvents(userId);
                showResults('remoteResults', result);
                updateStats();
            } catch (error) {
                showResults('remoteResults', { error: error.message });
            }
        }

        async function getStats() {
            try {
                const userId = getUserId();
                const result = await window.itineraryAPI.getDuplicateEventStats(userId);
                showResults('remoteResults', result);
                updateStats();
            } catch (error) {
                showResults('remoteResults', { error: error.message });
            }
        }

        async function cleanupDuplicates() {
            if (!confirm('Are you sure you want to cleanup duplicate events? This action cannot be undone.')) {
                return;
            }
            
            try {
                const userId = getUserId();
                const result = await window.itineraryAPI.cleanupDuplicateEvents(userId);
                showResults('remoteResults', result);
                updateStats();
            } catch (error) {
                showResults('remoteResults', { error: error.message });
            }
        }

        async function runComprehensiveCleanup() {
            if (!confirm('Are you sure you want to run comprehensive cleanup? This will clean both local and remote duplicates.')) {
                return;
            }

            try {
                const userId = getUserId();
                const localEvents = JSON.parse(localStorage.getItem('itineraryEvents') || '[]');
                
                if (window.eventCleanupUtils) {
                    const result = await window.eventCleanupUtils.comprehensiveEventCleanup(userId, localEvents, true);
                    
                    // Save cleaned events back to local storage
                    if (result.cleanedEvents) {
                        localStorage.setItem('itineraryEvents', JSON.stringify(result.cleanedEvents));
                    }
                    
                    showResults('comprehensiveResults', result);
                    updateStats();
                } else {
                    showResults('comprehensiveResults', { error: 'Event cleanup utils not loaded' });
                }
            } catch (error) {
                showResults('comprehensiveResults', { error: error.message });
            }
        }

        function updateStats() {
            // Update local event count
            const localEvents = JSON.parse(localStorage.getItem('itineraryEvents') || '[]');
            document.getElementById('localEventCount').textContent = localEvents.length;
            
            // Try to get remote stats
            const userId = getUserId();
            if (window.itineraryAPI) {
                window.itineraryAPI.getDuplicateEventStats(userId)
                    .then(result => {
                        if (result.stats) {
                            document.getElementById('remoteEventCount').textContent = result.stats.total_events || 0;
                            document.getElementById('duplicateCount').textContent = result.stats.total_duplicates || 0;
                        }
                    })
                    .catch(error => {
                        console.error('Error getting remote stats:', error);
                    });
            }
        }

        // Initialize stats on page load
        document.addEventListener('DOMContentLoaded', updateStats);
    </script>
</body>
</html>
