/**
 * Event Cleanup Utilities for Vestigia
 * 
 * This file provides utility functions for cleaning up and deduplicating
 * user events both locally and in Supabase.
 */

/**
 * Local event deduplication function
 * Removes duplicate events from an array based on content similarity
 * @param {Array} events - Array of events to deduplicate
 * @param {string} userId - User ID to filter by (optional)
 * @returns {Array} Deduplicated array of events
 */
function deduplicateEventsLocally(events, userId = null) {
  console.log('=== Starting Local Event Deduplication ===');
  console.log(`Initial event count: ${events.length}`);
  
  if (!events || events.length === 0) {
    return [];
  }

  // Filter by user ID if provided
  let filteredEvents = userId ? events.filter(event => event.user_id === userId) : events;
  
  // Create a map to store unique events
  const uniqueEvents = new Map();

  // Sort events by updated_at to keep the most recent version
  const sortedEvents = [...filteredEvents].sort((a, b) => {
    const dateA = a.updated_at ? new Date(a.updated_at) : new Date(0);
    const dateB = b.updated_at ? new Date(b.updated_at) : new Date(0);
    return dateB - dateA; // Most recent first
  });

  // Process each event
  sortedEvents.forEach(event => {
    if (!event || !event.id) {
      console.log('Skipping invalid event:', event);
      return;
    }

    // Create a content-based key for deduplication
    const contentKey = createEventContentKey(event);

    // If we haven't seen this content before, add it
    if (!uniqueEvents.has(contentKey)) {
      uniqueEvents.set(contentKey, event);
      console.log(`Keeping unique event: ${event.title} (${event.id})`);
    } else {
      console.log(`Removing duplicate event: ${event.title} (${event.id})`);
    }
  });

  const deduplicatedEvents = Array.from(uniqueEvents.values());
  console.log(`Final event count after deduplication: ${deduplicatedEvents.length}`);
  console.log(`Removed ${filteredEvents.length - deduplicatedEvents.length} duplicate events`);
  
  return deduplicatedEvents;
}

/**
 * Create a content-based key for event deduplication
 * @param {Object} event - Event object
 * @returns {string} Content key for deduplication
 */
function createEventContentKey(event) {
  // Normalize values for comparison
  const normalizeString = (str) => (str || '').toString().trim().toLowerCase();
  const normalizeDate = (date) => date ? new Date(date).toISOString() : '';

  return JSON.stringify({
    user_id: event.user_id || '',
    title: normalizeString(event.title),
    start: normalizeDate(event.start || event.start_time),
    end: normalizeDate(event.end || event.end_time),
    description: normalizeString(event.description),
    location: normalizeString(event.location),
    category: normalizeString(event.category)
  });
}

/**
 * Check if two events are duplicates based on content
 * @param {Object} event1 - First event
 * @param {Object} event2 - Second event
 * @returns {boolean} True if events are duplicates
 */
function areEventsDuplicates(event1, event2) {
  if (!event1 || !event2) return false;
  
  // Must be from the same user
  if (event1.user_id !== event2.user_id) return false;
  
  return createEventContentKey(event1) === createEventContentKey(event2);
}

/**
 * Clean up duplicate events for a user using the API
 * @param {string} userId - User ID
 * @param {boolean} dryRun - If true, only find duplicates without deleting
 * @returns {Promise<Object>} Cleanup results
 */
async function cleanupUserEventDuplicates(userId, dryRun = false) {
  try {
    console.log(`${dryRun ? 'Finding' : 'Cleaning up'} duplicate events for user: ${userId}`);
    
    if (!window.itineraryAPI) {
      throw new Error('Itinerary API not available');
    }

    let result;
    if (dryRun) {
      result = await window.itineraryAPI.findDuplicateEvents(userId);
    } else {
      result = await window.itineraryAPI.cleanupDuplicateEvents(userId);
    }

    console.log('Cleanup result:', result);
    return result;
  } catch (error) {
    console.error('Error during event cleanup:', error);
    throw error;
  }
}

/**
 * Get duplicate event statistics for a user
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Statistics about duplicate events
 */
async function getUserEventDuplicateStats(userId) {
  try {
    console.log(`Getting duplicate event statistics for user: ${userId}`);
    
    if (!window.itineraryAPI) {
      throw new Error('Itinerary API not available');
    }

    const result = await window.itineraryAPI.getDuplicateEventStats(userId);
    console.log('Duplicate stats:', result);
    return result;
  } catch (error) {
    console.error('Error getting duplicate stats:', error);
    throw error;
  }
}

/**
 * Comprehensive event cleanup that handles both local and remote duplicates
 * @param {string} userId - User ID
 * @param {Array} localEvents - Events from local storage
 * @param {boolean} syncWithSupabase - Whether to also clean up Supabase duplicates
 * @returns {Promise<Object>} Comprehensive cleanup results
 */
async function comprehensiveEventCleanup(userId, localEvents = [], syncWithSupabase = true) {
  console.log('=== Starting Comprehensive Event Cleanup ===');
  
  const results = {
    local: {
      before: localEvents.length,
      after: 0,
      removed: 0
    },
    remote: {
      duplicates_removed: 0,
      error: null
    },
    success: true,
    errors: []
  };

  try {
    // 1. Clean up local events
    console.log('Step 1: Cleaning up local events...');
    const cleanedLocalEvents = deduplicateEventsLocally(localEvents, userId);
    results.local.after = cleanedLocalEvents.length;
    results.local.removed = localEvents.length - cleanedLocalEvents.length;
    
    // 2. Clean up remote events if requested
    if (syncWithSupabase && userId) {
      console.log('Step 2: Cleaning up remote events...');
      try {
        const remoteCleanup = await cleanupUserEventDuplicates(userId, false);
        results.remote.duplicates_removed = remoteCleanup.duplicates_removed || 0;
      } catch (error) {
        console.error('Error cleaning up remote events:', error);
        results.remote.error = error.message;
        results.errors.push(`Remote cleanup failed: ${error.message}`);
      }
    }

    // 3. Summary
    const totalRemoved = results.local.removed + results.remote.duplicates_removed;
    console.log(`Cleanup complete. Total duplicates removed: ${totalRemoved}`);
    console.log(`- Local: ${results.local.removed}`);
    console.log(`- Remote: ${results.remote.duplicates_removed}`);
    
    if (results.errors.length > 0) {
      results.success = false;
      console.warn('Cleanup completed with errors:', results.errors);
    }

    return {
      ...results,
      cleanedEvents: cleanedLocalEvents,
      totalRemoved
    };

  } catch (error) {
    console.error('Error during comprehensive cleanup:', error);
    results.success = false;
    results.errors.push(error.message);
    throw error;
  }
}

/**
 * Validate event data before saving to prevent duplicates
 * @param {Object} newEvent - New event to validate
 * @param {Array} existingEvents - Existing events to check against
 * @returns {Object} Validation result with isDuplicate flag and details
 */
function validateEventForDuplicates(newEvent, existingEvents) {
  if (!newEvent || !existingEvents) {
    return { isDuplicate: false, reason: 'Invalid input' };
  }

  // Check for exact duplicates
  const duplicateEvent = existingEvents.find(existing => 
    areEventsDuplicates(newEvent, existing)
  );

  if (duplicateEvent) {
    return {
      isDuplicate: true,
      reason: 'Exact content match found',
      duplicateEvent: duplicateEvent,
      suggestion: 'Update existing event instead of creating new one'
    };
  }

  // Check for potential duplicates (same title and start time)
  const potentialDuplicate = existingEvents.find(existing => 
    existing.user_id === newEvent.user_id &&
    existing.title?.toLowerCase().trim() === newEvent.title?.toLowerCase().trim() &&
    new Date(existing.start || existing.start_time).getTime() === 
    new Date(newEvent.start || newEvent.start_time).getTime()
  );

  if (potentialDuplicate) {
    return {
      isDuplicate: false,
      isPotentialDuplicate: true,
      reason: 'Similar event found (same title and start time)',
      potentialDuplicateEvent: potentialDuplicate,
      suggestion: 'Review if this is intended to be a separate event'
    };
  }

  return { isDuplicate: false, reason: 'No duplicates found' };
}

// Export functions for use in other files
if (typeof window !== 'undefined') {
  window.eventCleanupUtils = {
    deduplicateEventsLocally,
    createEventContentKey,
    areEventsDuplicates,
    cleanupUserEventDuplicates,
    getUserEventDuplicateStats,
    comprehensiveEventCleanup,
    validateEventForDuplicates
  };
  
  console.log('Event cleanup utilities loaded and ready to use');
}
