/**
 * Transportation Reservations Management
 * Handles external reservation modal and localStorage operations
 */

// Global object to expose functions to other scripts
window.transportationReservations = {};

document.addEventListener('DOMContentLoaded', function () {
    // DOM Elements
    const addExternalReservationBtn = document.getElementById('addExternalReservation');
    const externalReservationModal = document.getElementById('externalReservationModal');

    // Check if modal exists before trying to access its properties
    if (!externalReservationModal) {
        console.error('External reservation modal not found in the DOM');
        return;
    }

    const closeModalBtns = externalReservationModal.querySelectorAll('.close-modal, .cancel-modal-btn');
    const externalReservationForm = document.getElementById('externalReservationForm');
    const reservedList = document.getElementById('reservedList');

    // Date pickers are now initialized in date-picker-init.js

    // Load and display existing reservations
    loadReservations();

    // Event Listeners

    // Open modal when Add External Booking button is clicked
    if (addExternalReservationBtn) {
        console.log('Add External Reservation button found, adding click event listener');
        addExternalReservationBtn.addEventListener('click', function () {
            console.log('Add External Reservation button clicked');
            openExternalReservationModal();
        });
    } else {
        console.error('Add External Reservation button not found in the DOM');
    }

    // Close modal when close button or cancel button is clicked
    closeModalBtns.forEach(btn => {
        btn.addEventListener('click', function () {
            closeExternalReservationModal();
        });
    });

    // Close modal when clicking outside the modal content
    window.addEventListener('click', function (event) {
        if (event.target === externalReservationModal) {
            closeExternalReservationModal();
        }
    });

    // Trip type selection removed as reservations are logically one-way

    // Handle form submission
    if (externalReservationForm) {
        externalReservationForm.addEventListener('submit', function (e) {
            e.preventDefault();
            saveReservation();
        });
    }

    // Functions

    // Open the external reservation modal
    function openExternalReservationModal(reservationToEdit = null) {
        // Make this function available globally
        window.transportationReservations.openExternalReservationModal = openExternalReservationModal;
        console.log('Opening external reservation modal');

        // Check if modal and form exist
        if (!externalReservationModal || !externalReservationForm) {
            console.error('Modal or form not found');
            return;
        }

        // Reset form
        externalReservationForm.reset();

        // Check if externalReservationId exists
        const reservationIdField = document.getElementById('externalReservationId');
        if (reservationIdField) {
            reservationIdField.value = '';
        } else {
            console.error('externalReservationId field not found');
        }

        // Trip type and return date removed as reservations are logically one-way

        // If editing an existing reservation, populate the form
        if (reservationToEdit) {
            document.getElementById('externalReservationId').value = reservationToEdit.id;
            document.getElementById('bookingType').value = reservationToEdit.type;
            document.getElementById('externalFrom').value = reservationToEdit.from;
            document.getElementById('externalTo').value = reservationToEdit.to;
            document.getElementById('externalDate').value = reservationToEdit.date;
            document.getElementById('externalTime').value = reservationToEdit.time;
            document.getElementById('externalCarrier').value = reservationToEdit.carrier;
            document.getElementById('externalReference').value = reservationToEdit.reference;
            document.getElementById('externalNotes').value = reservationToEdit.notes;

            // Return date handling removed as reservations are logically one-way
        }

        // Show modal
        console.log('Displaying modal');
        externalReservationModal.style.display = 'block';
    }

    // Close the external reservation modal
    function closeExternalReservationModal() {
        externalReservationModal.style.display = 'none';
    }

    // Save reservation to localStorage and Supabase if user is logged in
    async function saveReservation() {
        // Make this function available globally
        window.transportationReservations.saveReservation = saveReservation;
        // Get form data
        const reservationId = document.getElementById('externalReservationId').value || generateId();
        const type = document.getElementById('bookingType').value;
        const from = document.getElementById('externalFrom').value;
        const to = document.getElementById('externalTo').value;
        const date = document.getElementById('externalDate').value;
        const time = document.getElementById('externalTime').value;
        const carrier = document.getElementById('externalCarrier').value;
        const reference = document.getElementById('externalReference').value;
        const notes = document.getElementById('externalNotes').value;
        // Create reservation object
        const reservation = {
            id: reservationId,
            type: type,
            from: from,
            to: to,
            date: date,
            time: time,
            carrier: carrier,
            reference: reference,
            notes: notes,
            tripType: 'one-way', // Always one-way as reservations are logically one-way
            createdAt: new Date().toISOString()
        };

        // Get existing reservations
        const reservations = getReservations();

        // Check if we're editing an existing reservation
        const existingIndex = reservations.findIndex(r => r.id === reservationId);

        if (existingIndex !== -1) {
            // Update existing reservation and linked event
            await updateReservationAndEvent(reservationId, reservation);
        } else {
            // Add new reservation
            reservations.push(reservation);

            // Save to localStorage
            localStorage.setItem('transportationReservations', JSON.stringify(reservations));

            // If user is logged in, also save to Supabase
            if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
                try {
                    console.log(`Saving new reservation to Supabase for user ${auth.user.id}`);

                    // Add user_id to the reservation for Supabase
                    const supabaseReservation = {
                        ...reservation,
                        user_id: auth.user.id
                    };

                    // Save to Supabase via Netlify function
                    window.itineraryAPI.saveTransportationReservation(supabaseReservation)
                        .then(result => {
                            console.log('New reservation saved to Supabase:', result);
                        })
                        .catch(error => {
                            console.error('Error saving new reservation to Supabase:', error);
                            // Continue with local storage only if Supabase save fails
                        });
                } catch (error) {
                    console.error('Error saving new reservation to Supabase:', error);
                    // Continue with local storage only if Supabase save fails
                }
            } else {
                console.log('User is not logged in, saving new reservation to localStorage only');
            }

            // Refresh reservations list
            loadReservations();
        }

        // Close modal
        closeExternalReservationModal();
    }

    // Load reservations from localStorage and Supabase, then display them
    async function loadReservations() {
        // Show loading state
        reservedList.innerHTML = `
            <div class="loading-reservations">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading reservations...</p>
            </div>
        `;

        try {
            // Get reservations (this now includes Supabase sync)
            const reservations = await getReservations();

            // Clear the list
            reservedList.innerHTML = '';

            if (reservations.length === 0) {
                // Show empty state
                reservedList.innerHTML = `
                    <div class="empty-reservations">
                        <i class="fas fa-ticket-alt"></i>
                        <p>No reservations yet</p>
                        <p class="hint">Your booked transportation will appear here</p>
                    </div>
                `;
                return;
            }

            // Sort reservations by date (earliest first)
            reservations.sort((a, b) => {
                const dateA = new Date(a.date + 'T' + (a.time || '00:00'));
                const dateB = new Date(b.date + 'T' + (b.time || '00:00'));
                return dateA - dateB;
            });

            // Create reservation cards
            for (const reservation of reservations) {
                const card = await createReservationCard(reservation);
                reservedList.appendChild(card);
            }
        } catch (error) {
            console.error('Error loading reservations:', error);

            // Show error state
            reservedList.innerHTML = `
                <div class="error-reservations">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Error loading reservations</p>
                    <p class="hint">Please try again later</p>
                </div>
            `;
        }
    }

    // Create a reservation card element
    async function createReservationCard(reservation) {
        const card = document.createElement('div');
        card.className = 'reservation-card';
        card.dataset.id = reservation.id;

        // Format date
        const formattedDate = formatDate(reservation.date);

        // Get icon based on type
        let typeIcon = 'fa-plane';
        if (reservation.type === 'train') {
            typeIcon = 'fa-train';
        } else if (reservation.type === 'bus') {
            typeIcon = 'fa-bus';
        }

        // Extract IATA codes from the from/to fields
        const fromCode = extractIATACode(reservation.from);
        const toCode = extractIATACode(reservation.to);

        // Check if this reservation already has an itinerary event
        const existingEventId = await hasItineraryEvent(reservation.id);

        // Determine button content and class based on whether event exists
        let buttonContent, buttonClass, buttonTitle;
        if (existingEventId) {
            buttonContent = '<i class="fas fa-calendar-check"></i> View in Itinerary';
            buttonClass = 'add-to-itinerary-btn already-added';
            buttonTitle = 'View this reservation in your itinerary';
        } else {
            buttonContent = '<i class="fas fa-calendar-plus"></i> Add to Itinerary';
            buttonClass = 'add-to-itinerary-btn';
            buttonTitle = 'Add this reservation to your itinerary';
        }

        card.innerHTML = `
            <div class="reservation-header">
                <div class="reservation-type">
                    <i class="fas ${typeIcon}"></i>
                    <span>${reservation.type.charAt(0).toUpperCase() + reservation.type.slice(1)}</span>
                </div>
                <div class="reservation-actions">
                    <button class="edit-reservation" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="delete-reservation" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="reservation-route">
                <div class="route-point">
                    <span class="point-label">From:</span>
                    <span class="point-value">${fromCode}</span>
                </div>
                <div class="route-arrow">
                    <i class="fas fa-long-arrow-alt-right"></i>
                </div>
                <div class="route-point">
                    <span class="point-label">To:</span>
                    <span class="point-value">${toCode}</span>
                </div>
            </div>
            <div class="reservation-details">
                <div class="detail-item">
                    <i class="far fa-calendar-alt"></i>
                    <span>${formattedDate}</span>
                </div>
                ${reservation.time ? `
                <div class="detail-item">
                    <i class="far fa-clock"></i>
                    <span>${formatTime(reservation.time)}</span>
                </div>
                ` : ''}
                ${reservation.carrier ? `
                <div class="detail-item">
                    <i class="fas fa-building"></i>
                    <span>${reservation.carrier}</span>
                </div>
                ` : ''}
                ${reservation.reference ? `
                <div class="detail-item">
                    <i class="fas fa-hashtag"></i>
                    <span>Ref: ${reservation.reference}</span>
                </div>
                ` : ''}
            </div>
            ${reservation.notes ? `
            <div class="reservation-notes">
                <i class="fas fa-sticky-note"></i>
                <p>${reservation.notes}</p>
            </div>
            ` : ''}
            <div class="reservation-footer">
                <button class="${buttonClass}" title="${buttonTitle}">
                    ${buttonContent}
                </button>
            </div>
        `;

        // Add event listeners to the card buttons

        // Edit button
        card.querySelector('.edit-reservation').addEventListener('click', async function () {
            const reservationId = card.dataset.id;
            const reservations = await getReservations();
            const reservation = reservations.find(r => r.id === reservationId);
            if (reservation) {
                openExternalReservationModal(reservation);
            }
        });

        // Delete button
        card.querySelector('.delete-reservation').addEventListener('click', function () {
            const reservationId = card.dataset.id;
            if (confirm('Are you sure you want to delete this reservation? This will also remove any linked itinerary events.')) {
                deleteReservationAndEvent(reservationId);
            }
        });

        // Add to Itinerary button
        const addToItineraryBtn = card.querySelector('.add-to-itinerary-btn');
        if (existingEventId) {
            // If event already exists, navigate to itinerary
            addToItineraryBtn.addEventListener('click', function () {
                window.location.href = 'itinerary.html';
            });
        } else {
            // If no event exists, add to itinerary
            addToItineraryBtn.addEventListener('click', function () {
                const reservationId = card.dataset.id;
                addToItinerary(reservationId, addToItineraryBtn);
            });
        }

        return card;
    }

    // Delete a reservation from localStorage and Supabase if user is logged in
    function deleteReservation(id) {
        let reservations = getReservations();
        reservations = reservations.filter(r => r.id !== id);
        localStorage.setItem('transportationReservations', JSON.stringify(reservations));

        // If user is logged in, also delete from Supabase
        if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
            try {
                console.log(`Deleting reservation ${id} from Supabase for user ${auth.user.id}`);
                window.itineraryAPI.deleteTransportationReservation(auth.user.id, id)
                    .then(result => {
                        console.log('Reservation deleted from Supabase:', result);
                    })
                    .catch(error => {
                        console.error('Error deleting reservation from Supabase:', error);
                        // Continue with local storage update even if Supabase delete fails
                    });
            } catch (error) {
                console.error('Error deleting reservation from Supabase:', error);
                // Continue with local storage update even if Supabase delete fails
            }
        } else {
            console.log('User is not logged in, deleting from localStorage only');
        }

        loadReservations();
    }

    // Check if a reservation already has an associated itinerary event
    async function hasItineraryEvent(reservationId) {
        try {
            // Check localStorage events first
            const events = JSON.parse(localStorage.getItem('events') || '[]');
            const linkedEvent = events.find(event =>
                event.transportationReservationId === reservationId ||
                (event.category === 'transportation' && event.description && event.description.includes(`Reservation ID: ${reservationId}`))
            );

            if (linkedEvent) {
                return linkedEvent.id;
            }

            // If user is logged in, also check Supabase
            if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
                try {
                    const response = await fetch(`${window.itineraryAPI.getBaseUrl()}/user-events?user_id=${auth.user.id}`);
                    if (response.ok) {
                        const supabaseEvents = await response.json();
                        const linkedSupabaseEvent = supabaseEvents.find(event =>
                            event.transportation_reservation_id === reservationId ||
                            (event.category === 'transportation' && event.description && event.description.includes(`Reservation ID: ${reservationId}`))
                        );

                        if (linkedSupabaseEvent) {
                            return linkedSupabaseEvent.event_id;
                        }
                    }
                } catch (error) {
                    console.error('Error checking Supabase for linked events:', error);
                }
            }

            return null;
        } catch (error) {
            console.error('Error checking for linked itinerary event:', error);
            return null;
        }
    }

    // Add reservation to itinerary
    async function addToItinerary(id, buttonElement = null) {
        // Make this function available globally
        window.transportationReservations.addToItinerary = addToItinerary;

        // Use the provided button element or try to find it
        const button = buttonElement || document.querySelector(`[data-id="${id}"] .add-to-itinerary-btn`);
        let originalContent = '';

        if (button) {
            // Disable button and show loading state
            button.disabled = true;
            originalContent = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';

            // Add loading class for additional styling
            button.classList.add('loading');
        }

        try {
            // Check if this reservation already has an itinerary event
            const existingEventId = await hasItineraryEvent(id);
            if (existingEventId) {
                console.log('Reservation already has an itinerary event:', existingEventId);
                if (button) {
                    button.innerHTML = '<i class="fas fa-calendar-check"></i> View in Itinerary';
                    button.classList.remove('loading');
                    button.classList.add('already-added');
                    button.disabled = false;

                    // Change the button behavior to navigate to itinerary
                    button.onclick = () => {
                        window.location.href = 'itinerary.html';
                    };
                }
                return;
            }

            const reservations = await getReservations();
            const reservation = reservations.find(r => r.id === id);

            if (!reservation) {
                console.error('Reservation not found:', id);
                if (button) {
                    button.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
                    button.classList.add('error');
                    setTimeout(() => {
                        button.innerHTML = originalContent;
                        button.disabled = false;
                        button.classList.remove('loading', 'error');
                    }, 2000);
                }
                return;
            }

            // Extract IATA codes for cleaner display
            const fromCode = extractIATACode(reservation.from);
            const toCode = extractIATACode(reservation.to);

            // Generate a unique event ID that links to this reservation
            const eventId = `transport_${id}_${Date.now()}`;

            // Create event data for the itinerary with linking information
            const eventData = {
                id: eventId,
                title: `${reservation.type.charAt(0).toUpperCase() + reservation.type.slice(1)}: ${fromCode} → ${toCode}`,
                location: `From: ${reservation.from} To: ${reservation.to}`,
                startDate: reservation.date,
                startTime: reservation.time || '00:00',
                category: 'transportation',
                info: `Carrier: ${reservation.carrier || 'N/A'}\nReference: ${reservation.reference || 'N/A'}\nReservation ID: ${id}\n${reservation.notes || ''}`,
                transportationReservationId: id // Link back to reservation
            };

            // Store in localStorage for the itinerary page to use
            localStorage.setItem('pendingTransportationEvent', JSON.stringify(eventData));

            // Show success state briefly before redirect
            if (button) {
                button.innerHTML = '<i class="fas fa-check"></i> Added!';
                button.classList.remove('loading');
                button.classList.add('success');
            }

            // Redirect to itinerary page with parameter to open the modal after a brief delay
            setTimeout(() => {
                window.location.href = 'itinerary.html?openTransportationModal=true';
            }, 800);

        } catch (error) {
            console.error('Error adding reservation to itinerary:', error);
            if (button) {
                button.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
                button.classList.remove('loading');
                button.classList.add('error');
                setTimeout(() => {
                    button.innerHTML = originalContent;
                    button.disabled = false;
                    button.classList.remove('error');
                }, 2000);
            }
        }
    }

    // Update reservation and sync with linked itinerary event
    async function updateReservationAndEvent(reservationId, updatedReservation) {
        try {
            // Update the reservation
            const reservations = await getReservations();
            const reservationIndex = reservations.findIndex(r => r.id === reservationId);

            if (reservationIndex !== -1) {
                reservations[reservationIndex] = updatedReservation;
                localStorage.setItem('transportationReservations', JSON.stringify(reservations));

                // Save to Supabase if logged in
                if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
                    const supabaseReservation = { ...updatedReservation, user_id: auth.user.id };
                    await window.itineraryAPI.saveTransportationReservation(supabaseReservation);
                }

                // Check if there's a linked itinerary event and update it
                const linkedEventId = await hasItineraryEvent(reservationId);
                if (linkedEventId) {
                    await updateLinkedItineraryEvent(reservationId, updatedReservation, linkedEventId);
                }

                // Refresh the reservations display
                loadReservations();
            }
        } catch (error) {
            console.error('Error updating reservation and linked event:', error);
        }
    }

    // Update the linked itinerary event when reservation changes
    async function updateLinkedItineraryEvent(reservationId, reservation, eventId) {
        try {
            // Get current events
            const events = JSON.parse(localStorage.getItem('events') || '[]');
            const eventIndex = events.findIndex(e => e.id === eventId);

            if (eventIndex !== -1) {
                // Extract IATA codes for cleaner display
                const fromCode = extractIATACode(reservation.from);
                const toCode = extractIATACode(reservation.to);

                // Update the event with new reservation data
                events[eventIndex] = {
                    ...events[eventIndex],
                    title: `${reservation.type.charAt(0).toUpperCase() + reservation.type.slice(1)}: ${fromCode} → ${toCode}`,
                    location: `From: ${reservation.from} To: ${reservation.to}`,
                    start: reservation.date + (reservation.time ? 'T' + reservation.time : 'T00:00'),
                    description: `Carrier: ${reservation.carrier || 'N/A'}\nReference: ${reservation.reference || 'N/A'}\nReservation ID: ${reservationId}\n${reservation.notes || ''}`,
                    transportationReservationId: reservationId
                };

                // Save updated events to localStorage
                localStorage.setItem('events', JSON.stringify(events));

                // Save to Supabase if logged in
                if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
                    const eventForAPI = {
                        user_id: auth.user.id,
                        event_id: eventId,
                        title: events[eventIndex].title,
                        start: events[eventIndex].start,
                        end: events[eventIndex].end,
                        description: events[eventIndex].description,
                        location: events[eventIndex].location,
                        category: 'transportation',
                        transportation_reservation_id: reservationId
                    };
                    await window.itineraryAPI.saveUserEvent(eventForAPI);
                }

                console.log('Updated linked itinerary event:', eventId);
            }
        } catch (error) {
            console.error('Error updating linked itinerary event:', error);
        }
    }

    // Delete reservation and linked itinerary event
    async function deleteReservationAndEvent(reservationId) {
        try {
            // Check if there's a linked itinerary event
            const linkedEventId = await hasItineraryEvent(reservationId);

            // Delete the reservation
            let reservations = await getReservations();
            reservations = reservations.filter(r => r.id !== reservationId);
            localStorage.setItem('transportationReservations', JSON.stringify(reservations));

            // Delete from Supabase if logged in
            if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
                await window.itineraryAPI.deleteTransportationReservation(auth.user.id, reservationId);
            }

            // Delete the linked itinerary event if it exists
            if (linkedEventId) {
                await deleteLinkedItineraryEvent(linkedEventId);
            }

            // Refresh the reservations display
            loadReservations();
        } catch (error) {
            console.error('Error deleting reservation and linked event:', error);
        }
    }

    // Delete the linked itinerary event
    async function deleteLinkedItineraryEvent(eventId) {
        try {
            // Remove from localStorage
            const events = JSON.parse(localStorage.getItem('events') || '[]');
            const filteredEvents = events.filter(e => e.id !== eventId);
            localStorage.setItem('events', JSON.stringify(filteredEvents));

            // Delete from Supabase if logged in
            if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
                await window.itineraryAPI.deleteUserEvent(eventId, auth.user.id);
            }

            console.log('Deleted linked itinerary event:', eventId);
        } catch (error) {
            console.error('Error deleting linked itinerary event:', error);
        }
    }

    // Helper Functions

    // Get reservations from localStorage and sync with Supabase if user is logged in
    async function getReservations() {
        // Make this function available globally
        window.transportationReservations.getReservations = getReservations;

        // Get reservations from localStorage
        let reservations = JSON.parse(localStorage.getItem('transportationReservations') || '[]');

        // If user is logged in, sync with Supabase
        if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
            try {
                console.log(`Syncing reservations with Supabase for user ${auth.user.id}`);

                // Sync reservations with Supabase
                const syncResult = await window.itineraryAPI.syncTransportationReservations(auth.user.id, reservations);
                console.log('Reservations synced with Supabase:', syncResult);

                // Check if we got any new or updated reservations from Supabase
                if (syncResult && syncResult.results) {
                    let hasChanges = false;

                    syncResult.results.forEach(result => {
                        if (result.success && result.reservation && result.action) {
                            // Convert Supabase reservation format to our local format
                            const supabaseReservation = {
                                id: result.reservation.reservation_id,
                                type: result.reservation.type,
                                from: result.reservation.from_location,
                                to: result.reservation.to_location,
                                date: result.reservation.departure_date,
                                time: result.reservation.departure_time,
                                carrier: result.reservation.carrier,
                                reference: result.reservation.reference,
                                notes: result.reservation.notes,
                                tripType: result.reservation.trip_type || 'one-way',
                                createdAt: result.reservation.created_at
                            };

                            // If this is a new or updated reservation, add/update it in our local reservations
                            if (result.action === 'inserted' || result.action === 'updated') {
                                // Check if we already have this reservation locally
                                const existingIndex = reservations.findIndex(r => r.id === supabaseReservation.id);

                                if (existingIndex !== -1) {
                                    // Update existing reservation
                                    reservations[existingIndex] = supabaseReservation;
                                } else {
                                    // Add new reservation
                                    reservations.push(supabaseReservation);
                                }

                                hasChanges = true;
                            }
                        }
                    });

                    // If we made changes, save back to localStorage
                    if (hasChanges) {
                        localStorage.setItem('transportationReservations', JSON.stringify(reservations));
                        console.log('Updated reservations in localStorage with data from Supabase');
                    }
                }
            } catch (syncError) {
                console.error('Error syncing reservations with Supabase:', syncError);
                // Continue with local storage data if Supabase sync fails
            }
        }

        return reservations;
    }

    // Generate a unique ID
    function generateId() {
        return 'res_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Format date for display
    function formatDate(dateString) {
        const options = { weekday: 'short', month: 'short', day: 'numeric', year: 'numeric' };
        return new Date(dateString).toLocaleDateString('en-US', options);
    }

    // Extract IATA code from airport string
    function extractIATACode(airportString) {
        if (!airportString) return '';

        // Try to extract a 3-letter code in parentheses like "New York (JFK)"
        const codeMatch = airportString.match(/\(([A-Z]{3})\)/);
        if (codeMatch && codeMatch[1]) {
            return codeMatch[1];
        }

        // If no parentheses, check if the string starts with a 3-letter code
        const startCodeMatch = airportString.match(/^([A-Z]{3})/);
        if (startCodeMatch && startCodeMatch[1]) {
            return startCodeMatch[1];
        }

        // If no code found, return the first 3 characters or the whole string if shorter
        return airportString.length > 3 ? airportString.substring(0, 3).toUpperCase() : airportString.toUpperCase();
    }

    // Format time for display
    function formatTime(timeString) {
        if (!timeString) return '';

        const [hours, minutes] = timeString.split(':');
        const hour = parseInt(hours, 10);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const hour12 = hour % 12 || 12;

        return `${hour12}:${minutes} ${ampm}`;
    }

    // Extract IATA code from location string (e.g., "New York (JFK)" -> "JFK")
    function extractIATACode(location) {
        if (!location) return '';

        // Look for pattern like "City (CODE)" or just "CODE"
        const match = location.match(/\(([A-Z]{3})\)$/);
        if (match) {
            return match[1];
        }

        // If no parentheses, check if the location itself is a 3-letter code
        if (/^[A-Z]{3}$/.test(location.trim())) {
            return location.trim();
        }

        // Otherwise return the first word or first 3 characters
        const words = location.split(' ');
        return words[0].substring(0, 3).toUpperCase();
    }

    // Add a reservation directly (used by flight search results)
    function addReservation(reservationData) {
        console.log('Adding reservation:', reservationData);

        // Generate a unique ID
        const reservationId = generateId();

        // Create the full reservation object
        const reservation = {
            id: reservationId,
            type: reservationData.type || 'flight',
            from: reservationData.from,
            to: reservationData.to,
            date: reservationData.date,
            time: reservationData.departureTime || '',
            carrier: reservationData.carrier || '',
            reference: reservationData.flightNumber || reservationData.reference || '',
            notes: reservationData.notes || '',
            tripType: 'one-way',
            createdAt: new Date().toISOString()
        };

        // Get existing reservations (this is synchronous here since we need to return the ID)
        const reservations = JSON.parse(localStorage.getItem('transportationReservations') || '[]');

        // Add new reservation
        reservations.push(reservation);

        // Save to localStorage
        localStorage.setItem('transportationReservations', JSON.stringify(reservations));

        // If user is logged in, also save to Supabase
        if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
            try {
                console.log(`Saving new reservation to Supabase for user ${auth.user.id}`);

                // Add user_id to the reservation for Supabase
                const supabaseReservation = {
                    ...reservation,
                    user_id: auth.user.id
                };

                // Save to Supabase via Netlify function
                window.itineraryAPI.saveTransportationReservation(supabaseReservation)
                    .then(result => {
                        console.log('New reservation saved to Supabase:', result);
                    })
                    .catch(error => {
                        console.error('Error saving new reservation to Supabase:', error);
                        // Continue with local storage only if Supabase save fails
                    });
            } catch (error) {
                console.error('Error saving new reservation to Supabase:', error);
                // Continue with local storage only if Supabase save fails
            }
        } else {
            console.log('User is not logged in, saving new reservation to localStorage only');
        }

        // Refresh reservations list
        loadReservations();

        return reservationId;
    }

    // Make addReservation available globally
    window.transportationReservations.addReservation = addReservation;
});
