// Netlify function for saving user events to Supabase
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
const supabase = createClient(supabaseUrl, supabaseKey);

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'Preflight request successful' })
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Parse request body
    const requestBody = JSON.parse(event.body);
    const { user_id, event_id, title, start, end, description, location, category } = requestBody;

    // Validate required parameters
    if (!user_id || !event_id || !title || !start) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameters: user_id, event_id, title, start' })
      };
    }

    // Upsert event into Supabase
    const { data, error } = await supabase
      .from('user_events')
      .upsert(
        {
          user_id,
          event_id,
          title,
          start_time: start,
          end_time: end || null,
          description,
          location,
          category,
          updated_at: new Date().toISOString()
        },
        { onConflict: 'event_id, user_id' }
      )
      .select();

    if (error) {
      console.error('Error saving user event to Supabase:', error);

      // Check if the error is because the table doesn't exist
      if (error.code === '42P01') {
        console.log('Table user_events does not exist, creating it...');

        // Create the table
        const { error: createError } = await supabase.rpc('create_user_events_table');

        if (createError) {
          console.error('Error creating user_events table:', createError);

          // Try to create the table directly with SQL
          const { error: sqlError } = await supabase.rpc('exec_sql', {
            sql_query: `
              CREATE TABLE IF NOT EXISTS user_events (
                id SERIAL PRIMARY KEY,
                user_id TEXT NOT NULL,
                event_id TEXT NOT NULL,
                title TEXT NOT NULL,
                start_time TIMESTAMP WITH TIME ZONE,
                end_time TIMESTAMP WITH TIME ZONE,
                description TEXT,
                location TEXT,
                category TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
              
              CREATE INDEX IF NOT EXISTS idx_user_events_user_id ON user_events(user_id);
              CREATE INDEX IF NOT EXISTS idx_user_events_event_id ON user_events(event_id);
              
              ALTER TABLE user_events ENABLE ROW LEVEL SECURITY;
              
              CREATE POLICY select_own_events ON user_events
                FOR SELECT USING (auth.uid()::text = user_id);
              
              CREATE POLICY insert_own_events ON user_events
                FOR INSERT WITH CHECK (auth.uid()::text = user_id);
              
              CREATE POLICY update_own_events ON user_events
                FOR UPDATE USING (auth.uid()::text = user_id);
              
              CREATE POLICY delete_own_events ON user_events
                FOR DELETE USING (auth.uid()::text = user_id);
            `
          });

          if (sqlError) {
            console.error('Error creating SQL function:', sqlError);

            // Fallback to direct insert if we can't create the function
            const { error: insertError } = await supabase
              .from('user_events')
              .insert([
                {
                  user_id: 'system_init',
                  event_id: 'system_init',
                  title: 'Table Initialization',
                  start_time: new Date().toISOString(),
                  description: 'This record was created to initialize the table',
                  category: 'System'
                }
              ]);

            if (insertError) {
              console.error('Error creating table via insert:', insertError);
            }
          }
        }

        if (createError) {
          console.error('Error creating user_events table:', createError);
          return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Failed to create user_events table', message: createError.message })
          };
        }

        console.log('Table created, trying insert again');

        // Try the insert again
        const { data: retryData, error: retryError } = await supabase
          .from('user_events')
          .insert([
            {
              user_id,
              event_id,
              title,
              start_time: start,
              end_time: end || null,
              description,
              location,
              category
            }
          ])
          .select();

        if (retryError) {
          console.error('Error saving user event after table creation:', retryError);
          return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Failed to save user event after table creation', message: retryError.message })
          };
        }

        return {
          statusCode: 201,
          headers,
          body: JSON.stringify({
            success: true,
            message: 'User event saved successfully after creating table',
            data: retryData
          })
        };
      }

      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ error: 'Failed to save user event', message: error.message })
      };
    }

    return {
      statusCode: 201,
      headers,
      body: JSON.stringify({
        success: true,
        message: 'User event saved successfully',
        data
      })
    };
  } catch (error) {
    console.error('Error in save-user-event function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message
      })
    };
  }
};
