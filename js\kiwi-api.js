/**
 * Kiwi.com API Client
 * 
 * This client provides methods to interact with the Kiwi.com Flight Search API.
 * Documentation: https://docs.kiwi.com/
 */

class KiwiAPI {
    constructor() {
        this.apiKey = 'YOUR_KIWI_API_KEY'; // Replace with your API key
        this.baseUrl = 'https://api.skypicker.com';
        this.searchTimeout = 60000; // 60 seconds
    }

    /**
     * Search for flights using Kiwi.com API
     * @param {Object} params - Search parameters
     * @param {Function} progressCallback - Callback for search progress updates
     * @returns {Promise<Object>} Flight search results
     */
    async searchFlights(params, progressCallback) {
        try {
            if (progressCallback) {
                progressCallback({ status: 'searching', message: 'Searching for flights...', progress: 50 });
            }

            // Format search parameters
            const searchParams = this.formatSearchParams(params);

            // Make the API request
            const response = await fetch(`${this.baseUrl}/flights`, {
                method: 'GET',
                headers: {
                    'apikey': this.api<PERSON>ey,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(searchParams)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to search flights');
            }

            const data = await response.json();

            if (progressCallback) {
                progressCallback({ status: 'complete', message: `Found ${data.data.length} flights`, progress: 100 });
            }

            return {
                status: 'complete',
                data: data.data,
                currency: data.currency,
                currency_rates: data.currency_rates
            };
        } catch (error) {
            console.error('Error searching flights:', error);

            if (progressCallback) {
                progressCallback({ status: 'error', message: error.message, progress: 0 });
            }

            throw error;
        }
    }

    /**
     * Format search parameters according to Kiwi.com API requirements
     * @param {Object} params - Raw search parameters
     * @returns {Object} Formatted parameters
     */
    formatSearchParams(params) {
        // Extract IATA codes from full airport names
        const extractIATACode = (airportString) => {
            const match = airportString.match(/^([A-Z]{3})/);
            return match ? match[1] : null;
        };

        const originCode = extractIATACode(params.origin);
        const destinationCode = extractIATACode(params.destination);

        if (!originCode || !destinationCode) {
            throw new Error('Invalid airport codes');
        }

        return {
            fly_from: originCode,
            fly_to: destinationCode,
            date_from: params.departureDate,
            date_to: params.returnDate || params.departureDate,
            adults: params.adults || 1,
            children: params.children || 0,
            infants: params.infants || 0,
            curr: 'USD',
            locale: 'en',
            limit: 20,
            sort: 'price',
            asc: 1
        };
    }
}

// Create a global kiwiAPI object
window.kiwiAPI = new KiwiAPI(); 