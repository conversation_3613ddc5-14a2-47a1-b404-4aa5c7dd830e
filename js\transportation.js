/**
 * Transportation Page Functionality
 * Handles passenger selection and UI for transportation search
 */

document.addEventListener('DOMContentLoaded', function () {
    console.log('Transportation.js loaded');

    // Parse URL parameters
    function parseURLParams() {
        const urlParams = new URLSearchParams(window.location.search);
        const departure = urlParams.get('departure');
        const destination = urlParams.get('destination');

        console.log('URL parameters:', { departure, destination });

        // If we have parameters, populate the form
        if (departure && destination) {
            // Set the departure and destination inputs
            const departureInput = document.getElementById('departureInput');
            const destinationInput = document.getElementById('destinationInput');

            if (departureInput && destinationInput) {
                departureInput.value = departure;
                destinationInput.value = destination;

                // Set the selection flags to prevent suggestions from showing
                departureInput.dataset.selectionMade = 'true';
                destinationInput.dataset.selectionMade = 'true';

                // Set trip type to one-way by default
                const oneWayRadio = document.getElementById('one-way');
                if (oneWayRadio) {
                    oneWayRadio.checked = true;

                    // Hide return date field
                    const returnDateContainer = document.getElementById('returnDateContainer');
                    if (returnDateContainer) {
                        returnDateContainer.style.display = 'none';
                    }
                }

                // Submit the form automatically after a short delay
                setTimeout(() => {
                    const transportForm = document.getElementById('transportForm');
                    if (transportForm) {
                        console.log('Auto-submitting form with URL parameters');
                        transportForm.dispatchEvent(new Event('submit'));
                    }
                }, 500);
            }
        }
    }

    // Call the function to parse URL parameters
    parseURLParams();

    // Handle trip type radio buttons to show/hide return date field
    const tripTypeRadios = document.querySelectorAll('input[name="tripType"]');
    const returnDateContainer = document.getElementById('returnDateContainer');

    if (tripTypeRadios.length > 0 && returnDateContainer) {
        tripTypeRadios.forEach(radio => {
            radio.addEventListener('change', function () {
                if (this.value === 'one-way') {
                    returnDateContainer.style.display = 'none';
                } else {
                    returnDateContainer.style.display = 'block';
                }
            });
        });

        // Initialize based on current selection
        const selectedTripType = document.querySelector('input[name="tripType"]:checked');
        if (selectedTripType && selectedTripType.value === 'one-way') {
            returnDateContainer.style.display = 'none';
        }
    }

    // Ensure the external reservation button works
    const addExternalReservationBtn = document.getElementById('addExternalReservation');
    if (addExternalReservationBtn) {
        console.log('Add External Reservation button found in transportation.js');
        addExternalReservationBtn.addEventListener('click', function () {
            console.log('Add External Reservation button clicked in transportation.js');
            const modal = document.getElementById('externalReservationModal');
            if (modal) {
                console.log('Showing external reservation modal');
                modal.style.display = 'block';
            } else {
                console.error('External reservation modal not found');
            }
        });
    } else {
        console.error('Add External Reservation button not found in transportation.js');
    }

    // DOM Elements
    const passengerSelector = document.querySelector('.passenger-selector');
    const passengerDropdown = document.querySelector('.passenger-dropdown');
    const passengerDisplay = document.getElementById('passengerDisplay');
    const adultsCount = document.getElementById('adultsCount');
    const childrenCount = document.getElementById('childrenCount');
    const passengerInput = document.getElementById('passengerInput');
    const transportForm = document.getElementById('transportForm');
    const counterBtns = document.querySelectorAll('.counter-btn');

    // Initialize passenger counts
    let adults = 1;
    let children = 0;

    // Toggle passenger dropdown
    if (passengerSelector) {
        passengerSelector.addEventListener('click', function (e) {
            // Don't toggle if clicking on a counter button (handled separately)
            if (e.target.classList.contains('counter-btn')) {
                return;
            }

            // Toggle dropdown visibility
            if (passengerDropdown.style.display === 'none' || !passengerDropdown.style.display) {
                passengerDropdown.style.display = 'block';
            } else {
                passengerDropdown.style.display = 'none';
            }
        });
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function (e) {
        if (passengerDropdown &&
            passengerDropdown.style.display === 'block' &&
            !passengerSelector.contains(e.target)) {
            passengerDropdown.style.display = 'none';
        }
    });

    // Handle counter buttons
    if (counterBtns.length > 0) {
        counterBtns.forEach(btn => {
            btn.addEventListener('click', function (e) {
                e.stopPropagation(); // Prevent dropdown from closing

                const type = this.dataset.type;
                const action = this.classList.contains('plus') ? 'add' : 'subtract';

                if (type === 'adults') {
                    if (action === 'add') {
                        adults = Math.min(adults + 1, 9); // Max 9 adults
                    } else {
                        adults = Math.max(adults - 1, 1); // Min 1 adult
                    }
                    adultsCount.textContent = adults;
                } else if (type === 'children') {
                    if (action === 'add') {
                        children = Math.min(children + 1, 9); // Max 9 children
                    } else {
                        children = Math.max(children - 1, 0); // Min 0 children
                    }
                    childrenCount.textContent = children;
                }

                // Update display and hidden input
                updatePassengerDisplay();
            });
        });
    }

    // Update passenger display text and hidden input value
    function updatePassengerDisplay() {
        const total = adults + children;
        passengerDisplay.textContent = total === 1
            ? '1 passenger'
            : `${total} passengers`;

        // Update hidden input for form submission
        passengerInput.value = total;

        // For debugging
        console.log(`Updated passengers: ${adults} adults, ${children} children, total: ${total}`);
    }

    // Handle form submission
    if (transportForm) {
        console.log('Transport form found, adding submit event listener');
        transportForm.addEventListener('submit', async function (e) {
            console.log('Form submitted!');
            e.preventDefault();

            // Get form data
            const departure = document.getElementById('departureInput').value;
            const destination = document.getElementById('destinationInput').value;
            const departDate = document.getElementById('departDate').value;
            const returnDate = document.getElementById('returnDate').value;
            const tripType = document.querySelector('input[name="tripType"]:checked').value;
            const passengers = passengerInput.value;

            // Validate form data
            if (!departure || !destination || !departDate) {
                alert('Please fill in all required fields');
                return;
            }

            if (tripType === 'round-trip' && !returnDate) {
                alert('Please select a return date for round-trip');
                return;
            }

            // Extract airport codes from the input values
            const departureCode = departure.split(' - ')[0].trim();
            const destinationCode = destination.split(' - ')[0].trim();

            // For debugging
            console.log('Search form submitted with data:', {
                departureCode,
                destinationCode,
                departDate,
                returnDate,
                tripType,
                passengers
            });

            // Show results container
            const resultsContainer = document.querySelector('.results-container');
            if (resultsContainer) {
                resultsContainer.style.display = 'block';

                // Update route display
                const routeDisplay = document.getElementById('routeDisplay');
                const dateDisplay = document.getElementById('dateDisplay');

                if (routeDisplay) {
                    routeDisplay.textContent = `${departureCode} → ${destinationCode}`;
                }

                if (dateDisplay) {
                    // Create a date object that respects the exact date without timezone adjustments
                    const [year, month, day] = departDate.split('-');
                    // Note: month is 0-indexed in JavaScript Date
                    const dateObj = new Date(year, parseInt(month) - 1, day);

                    const formattedDate = dateObj.toLocaleDateString('en-US', {
                        weekday: 'short',
                        month: 'short',
                        day: 'numeric'
                    });
                    dateDisplay.textContent = formattedDate;
                }

                // Show message in search results
                const searchResults = document.getElementById('searchResults');
                if (searchResults) {
                    searchResults.innerHTML = `
                        <div class="no-results">
                            <i class="fas fa-info-circle"></i>
                            <p>Flight search functionality has been removed.</p>
                            <p>The UI is preserved for demonstration purposes only.</p>
                        </div>
                    `;
                }

                // Visualize route on globe if available
                if (window.visualizeRoute) {
                    window.visualizeRoute(departure, destination);
                }
            }
        });
    }
});
