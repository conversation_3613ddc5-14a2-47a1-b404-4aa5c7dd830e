// Netlify function for retrieving user events from Supabase
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
const supabase = createClient(supabaseUrl, supabaseKey);

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'Preflight request successful' })
    };
  }

  // Only allow GET requests
  if (event.httpMethod !== 'GET') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Get user_id from query parameters
    const { user_id } = event.queryStringParameters || {};

    // Validate required parameters
    if (!user_id) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameter: user_id' })
      };
    }

    // Verify authentication if Authorization header is present
    let userId = user_id;
    const authHeader = event.headers.authorization || event.headers.Authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const { data: { user }, error: authError } = await supabase.auth.getUser(token);

      if (authError || !user) {
        console.error('Authentication error:', authError);
        return {
          statusCode: 401,
          headers,
          body: JSON.stringify({ error: 'Unauthorized', message: authError?.message || 'Invalid token' })
        };
      }

      // Override user_id with authenticated user's ID for security
      userId = user.id;

      // If the requested user_id doesn't match the authenticated user's ID
      if (user_id !== userId) {
        console.warn(`User ${userId} attempted to access events for user ${user_id}`);
        return {
          statusCode: 403,
          headers,
          body: JSON.stringify({ error: 'Forbidden', message: 'You can only access your own events' })
        };
      }
    }

    // Fetch events from Supabase
    const { data: events, error } = await supabase
      .from('user_events')
      .select('*')
      .eq('user_id', userId)
      .order('start_time', { ascending: true });

    if (error) {
      console.error('Error fetching events from Supabase:', error);

      // Check if the error is because the table doesn't exist
      if (error.code === '42P01') {
        console.log('Table user_events does not exist');
        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({ events: [] })
        };
      }

      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ error: 'Failed to fetch events', message: error.message })
      };
    }

    // Return successful response with events
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ events: events || [] })
    };
  } catch (error) {
    console.error('Unexpected error in get-user-events function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error', message: error.message })
    };
  }
};
