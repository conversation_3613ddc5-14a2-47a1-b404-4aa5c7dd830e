/* Global Styles */
:root {
  --dark-color: #262626;
  /* Dark Background */
  --main-color: #861818;
  /* Main Red Color */
  --highlight-color: #ffd700;
  /* New Gold highlight */
  --light-color: #f5f5f5;
  /* Light gray text */
  --text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  --transition: all 0.3s ease;
}

/* Import Home Page Styles */
@import url('home.css');

body {
  font-family: 'Arial', sans-serif;
  background-color: var(--dark-color);
  color: var(--light-color);
  margin: 0;
  padding-top: 0px;
  /* Adjust top padding to make content visible */
}

a {
  color: var(--highlight-color);
  text-decoration: none;
}

/* Navbar */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  padding-top: 0;
  /* Adjust top padding */
  background-color: rgba(38, 38, 38, 0.9);
  color: #fff;
  position: fixed;
  width: 100%;
  box-sizing: border-box;
  /* Ensure padding is included in the width */
  z-index: 1000;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  transition: background-color 0.3s ease;
}

.navbar .logo img {
  height: 40px;
}

.navbar .search-container {
  flex-grow: 1;
  /* Allow the search container to grow */
  display: flex;
  justify-content: center;
  /* Center the search bar */
  align-items: center;
  margin: 0 20px;
  /* Add margin to separate from logo and icons */
}

.navbar .search-bar {
  width: 100%;
  /* Take full width of the container */
  max-width: 500px;
  /* Limit the maximum width */
  border-radius: 20px;
  padding: 8px 15px;
  font-size: 14px;
  background-color: #5a5a5a;
  color: white;
  transition: width 0.3s ease;
}

.navbar .search-bar::placeholder {
  color: #fff;
}

.navbar .search-bar:focus {
  outline: none;
  background-color: #ffd700;
}

.nav-links {
  display: flex;
  list-style-type: none;
  align-items: center;
  /* Align items vertically */
}

.nav-item {
  position: relative;
  margin: 0 15px;
}

.nav-link {
  display: flex;
  align-items: center;
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
}

.nav-link i {
  font-size: 20px;
  margin-right: 8px;
}

.nav-link.active {
  color: var(--highlight-color);
  /* Highlight color for active link */
}

.nav-link.login-btn {
  background-color: var(--highlight-color);
  color: var(--dark-color);
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: bold;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.nav-link.login-btn:hover {
  background-color: var(--main-color);
  /* Slightly darker yellow for hover effect */
  color: var(--main-color);
  /* Dark red text color on hover */
}

.tooltip {
  display: none;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #ffd700;
  color: #1e1e1e;
  padding: 5px;
  font-size: 12px;
  border-radius: 4px;
  white-space: nowrap;
  z-index: 10;
}

.nav-item:hover .tooltip {
  display: block;
}

.nav-item:hover .nav-link {
  color: #ffd700;
}

.profile-btn:hover .dropdown-menu {
  display: block;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 110%;
  left: -37px;
  background-color: #1e1e1e;
  padding: 10px;
  border-radius: 5px;
  width: 70px;
  padding-left: 20px;
}

.dropdown-menu a {
  color: #fff;
  text-decoration: none;
  padding: 5px 0;
  display: block;
}

.dropdown-menu a:hover {
  color: #ffd700;
}

/* Mobile Hamburger Menu */
.hamburger-menu {
  display: none;
  position: relative;
}

.hamburger-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 30px;
  cursor: pointer;
}

.mobile-menu {
  position: fixed;
  /* Change to fixed to ensure it stays within the viewport */
  top: 0;
  left: 0;
  background-color: #1e1e1e;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  max-height: 80vh;
  overflow-y: auto;
  width: 100%;
  /* Ensure it takes the full width of the screen */
  text-align: center;
  /* Center align the text */
  display: flex;
  flex-direction: column;
  align-items: center;
  /* Center align the items */
  justify-content: center;
  /* Center align vertically */
}

.mobile-menu.open {
  transform: translateX(0);
}

.mobile-links {
  list-style-type: none;
  padding: 0;
  /* Remove padding */
  margin: 0;
  /* Remove margin */
  width: 100%;
  /* Ensure the links take the full width */
  display: flex;
  flex-direction: column;
  align-items: center;
  /* Center align the items */
}

.mobile-links li {
  margin: 20px 0;
  padding: 0;
  /* Remove padding */
  width: auto;
  /* Adjust width to auto */
}

.mobile-links a {
  color: #fff;
  text-decoration: none;
  font-size: 16px;
}

.mobile-links a:hover {
  color: #ffd700;
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  color: #fff;
  font-size: 30px;
  cursor: pointer;
}

/* Media Queries */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .hamburger-menu {
    display: block;
  }

  .navbar .logo {
    display: block;
  }

  .search-container {
    display: block;
  }

  .mobile-menu {
    display: none;
  }

  .mobile-menu.open {
    display: block;
  }

  .nav-item {
    margin: 0 10px;
  }

  .navbar .logo img {
    height: 30px;
  }

  .mobile-menu.open {
    transform: translateX(0);
  }

  .mobile-links {
    padding: 0;
  }

  .mobile-links li {
    margin: 15px 0;
  }
}

/* Show mobile menu when hamburger is clicked */
.hamburger-menu.open .mobile-menu {
  transform: translateX(0);
}

/* Hide mobile menu when clicking outside */
body.overlay-open {
  overflow: hidden;
}

body.overlay-open .mobile-menu {
  transform: translateX(0);
}

/* Search Bar */
.search-container {
  display: flex;
  align-items: center;
  margin: 0 15px;
  padding: 0;
}

.search-bar {
  border: none;
  border-radius: 20px;
  height: 25px;
  margin-top: 5px;
  font-size: 14px;
  width: 500px;
  transition: width 0.3s ease;
  background-color: #5a5a5a;
  color: white;
}

.search-bar::placeholder {
  color: #fff;
}

.search-bar:focus {
  outline: none;
  width: 200px;
  background-color: #ffd700;
  color: var(--main-color);
}

/* Navbar links */
.nav-links .search-container {
  display: flex;
  align-items: center;
  margin: 0 20px;
  padding: 0;
  /* Ensure no extra padding */
}

.nav-links .search-bar {
  width: 150px;
  /* Default width for desktop */
  border-radius: 20px;

  font-size: 14px;
}

/* Mobile menu styling */
.mobile-links .search-container {
  margin-top: 15px;
  margin-left: 20px;
  padding: 0;
  /* Remove extra padding in mobile menu */
}

.mobile-links .search-bar {
  width: 100%;
  border-radius: 20px;
  padding: 8px 15px;
  font-size: 14px;
}

/* Responsive for mobile */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .hamburger-menu {
    display: block;
  }

  .search-bar {
    width: 200px;
    /* Expand search bar for mobile */
  }

  .mobile-links .search-bar {
    width: 100%;
  }
}


/* Background Image Section */
#background-image {
  background-image: url('../images/vestigiaHomePicture.png');
  background-size: cover;
  background-position: center;
  height: 550px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--light-color);
  text-align: center;
}

#background-image .image-overlay {
  background: rgba(0, 0, 0, 0.5);
  padding: 30px;
  border-radius: 10px;
}

#background-image h2 {
  font-size: 2.5rem;
}

#background-image p {
  font-size: 1.2rem;
  margin-top: 10px;
}

/* Banner Section */
#banner {
  background-color: var(--main-color);
  color: var(--light-color);
  text-align: center;
  padding: 40px 20px 20px;
  margin-top: -10px;
}

#banner h2 {
  font-size: 3rem;
  color: var(--highlight-color);
}

#banner p {
  font-size: 1.5rem;
  color: var(--light-color);
}

#banner .button {
  background-color: var(--highlight-color);
  color: var(--dark-color);
  padding: 15px 30px;
  border-radius: 30px;
  font-size: 1.2rem;
  text-transform: uppercase;
  margin-top: 20px;
  display: inline-block;
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}


/* Main Content */
#main {
  padding: 80px 20px 20px;
  text-align: center;
}

#main .container {
  max-width: 1200px;
  margin: 0 auto;
}

#main h2 {
  font-size: 2.5rem;
  color: var(--highlight-color);
}

/* Feature Section */
.features {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 40px;
}

.feature {
  background-color: var(--dark-color);
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  width: 250px;
  text-align: center;
  margin-bottom: 20px;
}

.feature h3 {
  color: var(--highlight-color);
  font-size: 1.5rem;
  margin-top: 10px;
}

.feature p {
  color: var(--light-color);
}

.feature-icon {
  width: 50px;
  height: 50px;
  margin-bottom: 15px;
}

/* Footer */
#footer {
  background-color: var(--dark-color);
  color: var(--light-color);
  text-align: center;
  padding: 20px;
}

#footer p {
  font-size: 1rem;
}

/* About Page Banner */
#about-banner {
  background-color: var(--main-color);
  color: var(--light-color);
  text-align: center;
  padding: 60px 20px;
}

#about-banner h2 {
  font-size: 3rem;
  color: var(--highlight-color);
}

#about-banner p {
  font-size: 1.5rem;
  color: var(--light-color);
}

/* About Page Main Content */
#main {
  padding: 60px 20px 20px;
  text-align: center;
}

#main .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

#main h2 {
  font-size: 2.5rem;
  color: var(--highlight-color);
  margin-bottom: 20px;
}

#main p {
  color: var(--light-color);
  font-size: 1.2rem;
}

/* Footer (same as on the homepage) */
#footer {
  background-color: var(--dark-color);
  color: var(--light-color);
  text-align: center;
  padding: 20px;
}

#footer p {
  font-size: 1rem;
}

.contactInput {
  width: 50%;
  padding: 12px 20px;
  margin: 8px 0;
  display: inline-block;
  border: 1px solid #ccc;
  box-sizing: border-box;
  border-radius: 50px;
}

.messageButton {
  background-color: var(--highlight-color);
  color: var(--dark-color);
  padding: 15px 30px;
  border-radius: 30px;
  font-size: 1.2rem;
  text-transform: uppercase;
  margin-top: 20px;
  display: inline-block;
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}

a .nameLink {
  color: var(--highlight-color);
  text-decoration: none;
}

/* Adjusts for small screens */
@media screen and (max-width: 600px) {
  .topNav a:not(:first-child) {
    display: none;
    /* Hides menu items except the first one */
  }

  .topNav a.icon {
    display: block;
    /* Shows the menu icon */
  }

  .topNav.responsive {
    position: relative;
  }

  .topNav.responsive .icon {
    position: absolute;
    right: 0;
    top: 0;
  }

  .topNav.responsive .nav-links a {
    float: none;
    display: block;
    text-align: left;
  }
}