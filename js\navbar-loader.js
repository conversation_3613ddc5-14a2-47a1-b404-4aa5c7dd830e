/**
 * Navbar Loader
 * This script handles loading the navbar and setting the active link
 */

// Function to load navbar and set active links
function loadNavbar() {
  // Check if navbar placeholder exists
  const navbarPlaceholder = document.getElementById('navbar-placeholder');
  if (!navbarPlaceholder) {
    console.warn('Navbar placeholder not found');
    return;
  }

  // Determine which navbar to load based on the current page
  const isAuthPage = window.location.pathname.includes('auth-') || 
                    window.location.pathname.includes('login') || 
                    window.location.pathname.includes('signup');
  const navbarPath = isAuthPage ? 'auth-navbar.html' : 'navbar.html';
  
  // Load the appropriate navbar
  fetch(navbarPath)
    .then(response => {
      if (!response.ok) {
        throw new Error('Failed to load navbar');
      }
      return response.text();
    })
    .then(data => {
      // Insert navbar HTML
      navbarPlaceholder.innerHTML = data;

      // Initialize auth if available
      if (typeof auth !== 'undefined') {
        auth.init();
      }

      // Set active navigation link
      setActiveNavbarLink();
    })
    .catch(error => {
      console.error('Error loading navbar:', error);
    });
}

// Function to set active link in navbar
function setActiveNavbarLink() {
  // Get current page filename
  const currentPath = window.location.pathname;
  const currentPage = currentPath.split('/').pop() || 'index.html';

  console.log('Setting active navbar link for page:', currentPage);

  // Select all navigation links (both mobile and desktop)
  const navLinks = document.querySelectorAll('.nav-link');

  // Loop through all links and set active class
  navLinks.forEach(link => {
    const href = link.getAttribute('href');

    // Check if the current page matches the link's href
    if (href === currentPage ||
      (currentPage === '' && href === 'index.html') ||
      (currentPath === '/' && href === 'index.html')) {
      // Add active class
      link.classList.add('active');

      // For all menu icons, highlight the icon with !important to override any other styles
      if (link.querySelector('i')) {
        link.querySelector('i').style.cssText = 'color: #ffd700 !important';
      }

      console.log('Active link found and highlighted:', href);
    } else {
      // Remove active class
      link.classList.remove('active');

      // Reset icon color
      if (link.querySelector('i')) {
        link.querySelector('i').style.cssText = '';
      }
    }
  });
}

// Load navbar when DOM is ready
document.addEventListener('DOMContentLoaded', loadNavbar);
