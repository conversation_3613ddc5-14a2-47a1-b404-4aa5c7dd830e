-- Create feedback table for user suggestions, problems, and bug reports
CREATE TABLE IF NOT EXISTS public.feedback (
    id UUID DEFAULT gen_random_uuid () PRIMARY KEY,
    user_id UUID REFERENCES auth.users (id) ON DELETE SET NULL,
    email TEXT,
    name TEXT,
    feedback_type TEXT NOT NULL CHECK (
        feedback_type IN (
            'suggestion',
            'bug_report',
            'general_feedback'
        )
    ),
    subject TEXT NOT NULL,
    message TEXT NOT NULL,
    page_url TEXT,
    user_agent TEXT,
    status TEXT DEFAULT 'pending' CHECK (
        status IN (
            'pending',
            'reviewed',
            'in_progress',
            'resolved',
            'closed'
        )
    ),
    priority TEXT DEFAULT 'medium' CHECK (
        priority IN (
            'low',
            'medium',
            'high',
            'critical'
        )
    ),
    created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW()
);

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_feedback_user_id ON public.feedback (user_id);

CREATE INDEX IF NOT EXISTS idx_feedback_status ON public.feedback (status);

CREATE INDEX IF NOT EXISTS idx_feedback_created_at ON public.feedback (created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE public.feedback ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can insert feedback" ON public.feedback;

DROP POLICY IF EXISTS "Users can view own feedback" ON public.feedback;

DROP POLICY IF EXISTS "Service role can view all feedback" ON public.feedback;

-- Create RLS policies
-- Allow anyone to insert feedback (anonymous or authenticated)
CREATE POLICY "Anyone can insert feedback" ON public.feedback FOR
INSERT
WITH
    CHECK (true);

-- Users can view their own feedback (if authenticated)
CREATE POLICY "Users can view own feedback" ON public.feedback FOR
SELECT USING (
        auth.uid () = user_id
        OR auth.role () = 'service_role'
    );

-- Service role can do everything
CREATE POLICY "Service role can do everything" ON public.feedback FOR ALL USING (auth.role () = 'service_role');

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_feedback_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_feedback_updated_at
    BEFORE UPDATE ON public.feedback
    FOR EACH ROW
    EXECUTE FUNCTION update_feedback_updated_at();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;

GRANT ALL ON public.feedback TO anon, authenticated;

GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;