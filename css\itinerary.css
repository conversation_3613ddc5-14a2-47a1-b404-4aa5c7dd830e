body {
    background-color: #262626;
    color: #f5f5f5;
    font-family: 'Open Sans', sans-serif;
}

/* Main layout */
.itinerary-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    padding-top: 0;
    /* Remove top padding since we're using margin-top on sections */
}

@media (min-width: 992px) {
    .itinerary-container {
        grid-template-columns: 2fr 1fr;
    }
}

/* Calendar Section */
#calendar-section {
    margin-top: 100px;
    text-align: center;
    margin-bottom: 30px;
    background-color: #333;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

#calendar-section h2 {
    color: #ffd700;
    margin-bottom: 25px;
    font-size: 36px;
    font-weight: bold;
    position: relative;
    display: inline-block;
    text-shadow: 4px 4px 0 #861818;
    letter-spacing: 1px;
}

#calendar-section h2::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -8px;
    width: 100%;
    height: 4px;
    background-color: #861818;
}

#calendar-controls {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    gap: 15px;
}

#calendar-controls select {
    padding: 10px 15px;
    font-size: 16px;
    border-radius: 25px;
    background-color: #861818;
    color: #ffd700;
    outline: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

#calendar-controls select:hover {
    background-color: #6a1414;
}

#calendar {
    max-width: 100%;
    margin: 0 auto;
    background-color: #333;
    border-radius: 8px;
    overflow: hidden;
}

/* Calendar styling */
.fc-view-container {
    background-color: #333;
    border-radius: 8px;
}

.fc-day-header {
    background-color: #861818;
    color: #ffd700 !important;
    padding: 10px 0 !important;
    font-weight: bold;
}

.fc-day-number {
    font-size: 16px;
    padding: 8px !important;
}

.fc-today {
    background-color: rgba(255, 215, 0, 0.2) !important;
}

.fc-today .fc-day-number,
.fc-today .fc-content {
    color: #ffd700 !important;
    font-weight: bold;
}

.fc-day,
.fc-day-top {
    color: #ffd700 !important;
}

.fc-event {
    background-color: #861818 !important;
    border-color: #861818 !important;
    color: #ffd700 !important;
    border-radius: 4px !important;
    padding: 5px !important;
    margin: 2px 0 !important;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
    z-index: 5;
}

.fc-event::after {
    content: '\f06e';
    /* Eye icon */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.fc-event:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 0 2px rgba(255, 215, 0, 0.5);
}

.fc-event:hover::after {
    opacity: 1;
}

/* Pulse highlight animation for calendar events */
@keyframes pulse-highlight {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
    }
}

.pulse-highlight {
    animation: pulse-highlight 1s infinite;
    z-index: 100 !important;
}

/* Day with events indicator */
.has-events {
    position: relative;
}

.has-events:after {
    content: '';
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background-color: #ffd700;
    border-radius: 50%;
}

/* Saved Places Section */
#saved-places-section {
    margin-top: 100px;
    text-align: center;
    background-color: #333;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

#saved-places-section h2 {
    color: #ffd700;
    margin-bottom: 20px;
    font-size: 24px;
}

.saved-places {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 600px;
    /* Increased height to fit three items */
    overflow-y: auto;
    scrollbar-width: thick;
    /* Changed to thick for Firefox */
    scrollbar-color: #ffd700 #333;
}

.saved-places::-webkit-scrollbar {
    width: 12px;
}

.saved-places::-webkit-scrollbar-track {
    background: #333;
    border: 1px solid #555;
}

.saved-places::-webkit-scrollbar-thumb {
    background-color: #861818;
    border-radius: 6px;
    border: 2px solid #ffd700;
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

.place-item {
    background: #444;
    margin: 10px 0;
    padding: 15px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    text-align: left;
    border-left: 4px solid #861818;
    transition: transform 0.2s ease;
}

.place-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.place-info h3 {
    margin: 0 0 8px 0;
    color: #ffd700;
    font-size: 18px;
}

.place-info p {
    margin: 0 0 10px 0;
    color: #ddd;
    font-size: 14px;
}

.place-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    justify-content: flex-end;
}

.place-actions button {
    background-color: #861818;
    color: #ffd700;
    border: none;
    padding: 8px 12px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s ease;
}

.place-actions button i {
    font-size: 14px;
}

.place-actions button:hover {
    background-color: #6a1414;
    transform: translateY(-2px);
}

/* Modal Styling */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    /* Changed from auto to hidden */
    background-color: rgba(0, 0, 0, 0.7);
    padding-top: 125px;
    /* Added 10px more top margin */
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #333;
    margin: 0 auto;
    padding: 25px;
    border: 2px solid #861818;
    width: 90%;
    max-width: 500px;
    max-height: 70vh;
    /* Reduced from 80vh to 70vh to make it smaller */
    border-radius: 10px;
    box-sizing: border-box;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    animation: modalFadeIn 0.3s ease;
    overflow-y: auto;
    /* Add scrollbar inside the modal */
    scrollbar-width: thin;
    scrollbar-color: #861818 #333;
}

/* Webkit scrollbar styles for modal content */
.modal-content::-webkit-scrollbar {
    width: 8px;
}

.modal-content::-webkit-scrollbar-track {
    background: #333;
    border-radius: 10px;
}

.modal-content::-webkit-scrollbar-thumb {
    background-color: #861818;
    border-radius: 10px;
    border: 2px solid #333;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    color: #ffd700;
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    transition: all 0.2s ease;
}

.close:hover,
.close:focus {
    color: #fff;
    text-decoration: none;
    cursor: pointer;
    transform: scale(1.1);
}

#modal-title {
    color: #ffd700;
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 24px;
    text-align: center;
}

/* Form Styling */
.form-group {
    margin-bottom: 15px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-group.half {
    flex: 1;
}

#event-form label {
    display: block;
    margin: 0 0 8px;
    color: #ffd700;
    font-weight: bold;
}

#event-form label i {
    margin-right: 5px;
    width: 16px;
    text-align: center;
}

#event-form input,
#event-form textarea,
#event-form select {
    width: 100%;
    padding: 12px 15px;
    border-radius: 8px;
    box-sizing: border-box;
    background-color: #444;
    border: 1px solid #666;
    color: #fff;
    font-size: 16px;
    transition: all 0.2s ease;
}

#event-form input:focus,
#event-form textarea:focus,
#event-form select:focus {
    outline: none;
    border-color: #ffd700;
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

#event-form input::placeholder,
#event-form textarea::placeholder {
    color: #999;
}

#event-form textarea {
    min-height: 100px;
    resize: vertical;
}

#event-form .button-container {
    display: flex;
    justify-content: space-between;
    margin-top: 25px;
}

#event-form button {
    background-color: #861818;
    color: #ffd700;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.2s ease;
    min-width: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

#event-form button i {
    font-size: 16px;
}

#event-form button:hover {
    background-color: #6a1414;
    transform: translateY(-2px);
}

#delete-button {
    background-color: #444 !important;
}

#delete-button:hover {
    background-color: #d32f2f !important;
}

/* Form validation styling */
#event-form input:invalid,
#event-form textarea:invalid,
#event-form select:invalid {
    border-color: #d32f2f;
}

.validation-message {
    color: #d32f2f;
    font-size: 14px;
    margin-top: 5px;
    display: none;
}

.invalid .validation-message {
    display: block;
}

/* Event Details Modal Styling */
.event-details-content {
    padding: 10px 20px;
}

#details-modal-title {
    color: #ffd700;
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 26px;
    text-align: center;
    border-bottom: 2px solid #861818;
    padding-bottom: 4px;
}

#event-details-container {
    margin-bottom: 6px;
}

.event-detail-row {
    display: flex;
    margin-bottom: 0;
    align-items: flex-start;
    padding-bottom: 0;
    margin-top: 2px;
}

.event-detail-row:first-child {
    margin-top: 0;
}

.detail-label {
    flex: 0 0 140px;
    color: #ffd700;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    margin-bottom: 0;
    padding-bottom: 0;
}

.detail-label i {
    width: 18px;
    text-align: center;
    font-size: 14px;
}

.detail-value {
    flex: 1;
    color: #fff;
    line-height: 1.2;
    word-break: break-word;
    font-size: 14px;
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
}

#detail-title {
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    margin-bottom: 1px;
}

#detail-info {
    white-space: pre-line;
    background-color: #444;
    padding: 6px 8px;
    border-radius: 4px;
    border-left: 2px solid #861818;
    font-size: 13px;
}

.details-button-container {
    display: flex;
    justify-content: space-evenly;
    margin-top: 6px;
    border-top: 1px solid #555;
    padding-top: 6px;
    position: relative;
    min-height: 45px;
    /* Ensure space for buttons */
    gap: 15px;
    /* Equal spacing between all buttons */
}

@media (max-width: 768px) {
    .details-button-container {
        flex-direction: column;
        gap: 10px;
    }

    .secondary-button {
        padding: 6px 10px;
        font-size: 12px;
        width: 100%;
        justify-content: center;
    }
}

.detail-label {
    flex: 0 0 90px;
    font-size: 13px;
}

.event-detail-row {
    flex-direction: column;
    margin-bottom: 1px;
}

.detail-label {
    margin-bottom: 0;
}

.detail-value {
    padding-left: 15px;
    font-size: 13px;
}


#edit-event-button {
    background-color: #861818;
    color: #ffd700;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: normal;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

#edit-event-button:hover {
    background-color: #6a1414;
    transform: translateY(-2px);
}

#edit-event-button i {
    font-size: 12px;
}

.right-buttons {
    display: flex;
    gap: 15px;
    flex: 1;
    justify-content: flex-end;
}

.secondary-button {
    background-color: #444;
    color: #ffd700;
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.secondary-button:hover {
    background-color: #555;
    transform: translateY(-2px);
}

.secondary-button i {
    font-size: 12px;
}

/* Events List Section */
#events-list-section {
    margin-top: 40px;
    text-align: center;
    color: #ffffff;
    background-color: #333;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

#events-list-section h2 {
    color: #ffd700;
    margin-bottom: 20px;
    font-size: 24px;
}

.events-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.events-filter {
    display: flex;
    gap: 10px;
    align-items: center;
}

.events-filter select {
    padding: 8px 12px;
    border-radius: 20px;
    background-color: #444;
    color: #fff;
    border: 1px solid #666;
    outline: none;
}

.events-search {
    position: relative;
}

.events-search input {
    padding: 8px 12px 8px 35px;
    border-radius: 20px;
    background-color: #444;
    color: #fff;
    border: 1px solid #666;
    outline: none;
    width: 180px;
}

.events-search i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

#events-list {
    list-style-type: none;
    padding: 0;
    margin: 0 auto;
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #861818 #333;
}

#events-list::-webkit-scrollbar {
    width: 8px;
}

#events-list::-webkit-scrollbar-track {
    background: #333;
}

#events-list::-webkit-scrollbar-thumb {
    background-color: #861818;
    border-radius: 10px;
}

#events-list li {
    background: #444;
    margin: 10px 0;
    padding: 15px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    color: #fff;
    text-align: left;
    border-left: 4px solid #861818;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
    overflow: hidden;
}

#events-list li::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 215, 0, 0.05);
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    z-index: 1;
}

#events-list li:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(255, 215, 0, 0.3);
}

#events-list li:hover::after {
    opacity: 1;
}

#events-list li.no-events {
    background: transparent;
    border: 2px dashed #555;
    color: #aaa;
    text-align: center;
    padding: 30px 15px;
    font-style: italic;
    border-left: none;
}

#events-list li.no-events:hover {
    transform: none;
    box-shadow: none;
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    position: relative;
    z-index: 2;
}

.event-title {
    color: #ffd700;
    font-weight: bold;
    font-size: 18px;
    margin: 0;
}

.event-date {
    color: #ccc;
    font-size: 14px;
}

.event-location {
    color: #ddd;
    margin: 5px 0;
    display: flex;
    align-items: center;
    gap: 5px;
    position: relative;
    z-index: 2;
}

.event-location i {
    color: #ffd700;
    font-size: 14px;
}

.event-description {
    color: #bbb;
    margin: 5px 0 15px 0;
    font-size: 14px;
    position: relative;
    z-index: 2;
}

#events-list li .button-container {
    display: flex;
    gap: 8px;
    margin-top: auto;
    justify-content: flex-end;
    flex-wrap: nowrap;
    padding-top: 15px;
    width: 100%;
}

#events-list li button {
    background-color: #861818;
    color: #ffd700;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    min-width: 0;
    flex: 0 1 auto;
    white-space: nowrap;
}

#events-list li button i {
    font-size: 12px;
    flex-shrink: 0;
}

#events-list li button span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#events-list li button:hover {
    background: #6a1414;
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    #events-list li .button-container {
        flex-wrap: wrap;
        gap: 8px;
    }

    #events-list li button {
        flex: 1;
        min-width: 0;
        padding: 10px;
        font-size: 14px;
    }
}

.event-actions {
    display: flex;
    flex-wrap: wrap;
    /* Allow buttons to wrap to the next line */
    gap: 10px;
    /* Space between buttons */
    justify-content: flex-start;
    /* Align buttons to the start */
    margin-top: 15px;
    /* Add some space above buttons */
}

#events-list li button {
    background-color: #861818;
    color: #ffd700;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9em;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s ease, transform 0.2s ease;
    white-space: nowrap;
    /* Prevent text wrapping within buttons */
}

#events-list li button i {
    font-size: 1em;
}

#events-list li button span {
    font-weight: 600;
}

#events-list li button:hover {
    background-color: #a02020;
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    #events-list li .button-container {
        flex-direction: column;
        align-items: flex-start;
    }

    #events-list li button {
        width: 100%;
        margin-bottom: 8px;
        justify-content: center;
    }

    .event-actions {
        flex-direction: column;
        /* Stack buttons vertically on small screens */
        width: 100%;
    }
}