From c2a1b140554a3a5f07aec775b8a245d7993b1a04 Mon Sep 17 00:00:00 2001
From: daniel<PERSON>omanov<PERSON> <<EMAIL>>
Date: Tue, 10 Jun 2025 18:48:18 -0400
Subject: [PATCH] transportation update

---
 css/flight-search-results.css | 396 ++++++++++++++++++++++++++++++++++
 1 file changed, 396 insertions(+)
 create mode 100644 css/flight-search-results.css

diff --git a/css/flight-search-results.css b/css/flight-search-results.css
new file mode 100644
index 0000000..4dab19b
--- /dev/null
+++ b/css/flight-search-results.css
@@ -0,0 +1,396 @@
+/**
+ * Flight Search Results Styles
+ * Styles for flight search results, cards, and loading states
+ */
+
+/* Flight Search Container */
+.flight-search-container {
+    margin: 20px 0;
+}
+
+/* Loading Indicator */
+.loading-indicator {
+    display: flex;
+    flex-direction: column;
+    align-items: center;
+    justify-content: center;
+    padding: 40px 20px;
+    text-align: center;
+}
+
+.loading-indicator .spinner {
+    width: 40px;
+    height: 40px;
+    border: 4px solid rgba(255, 215, 0, 0.2);
+    border-radius: 50%;
+    border-top-color: #ffd700;
+    animation: spin 1s ease-in-out infinite;
+    margin-bottom: 15px;
+}
+
+@keyframes spin {
+    to { transform: rotate(360deg); }
+}
+
+.loading-indicator p {
+    color: #f5f5f5;
+    font-size: 16px;
+    margin: 0;
+}
+
+/* Error Container */
+.error-container {
+    display: flex;
+    align-items: center;
+    justify-content: center;
+    padding: 20px;
+    background-color: rgba(255, 107, 107, 0.1);
+    border: 1px solid rgba(255, 107, 107, 0.3);
+    border-radius: 8px;
+    margin: 20px 0;
+}
+
+.error-container i {
+    color: #ff6b6b;
+    font-size: 20px;
+    margin-right: 10px;
+}
+
+.error-container .error-message {
+    color: #ff6b6b;
+    font-weight: 500;
+}
+
+/* No Results */
+.no-results {
+    display: flex;
+    flex-direction: column;
+    align-items: center;
+    justify-content: center;
+    padding: 40px 20px;
+    text-align: center;
+    color: rgba(245, 245, 245, 0.6);
+}
+
+.no-results i {
+    font-size: 48px;
+    color: rgba(255, 215, 0, 0.3);
+    margin-bottom: 15px;
+}
+
+.no-results h3 {
+    color: #f5f5f5;
+    margin-bottom: 10px;
+}
+
+.no-results p {
+    margin: 5px 0;
+    font-style: italic;
+}
+
+/* Flight Results Container */
+.flight-results {
+    display: flex;
+    flex-direction: column;
+    gap: 15px;
+    margin-top: 20px;
+}
+
+/* Flight Card */
+.flight-card {
+    background: rgba(255, 255, 255, 0.05);
+    border-radius: 12px;
+    padding: 20px;
+    border: 1px solid rgba(255, 215, 0, 0.2);
+    transition: all 0.3s ease;
+    position: relative;
+    overflow: hidden;
+}
+
+.flight-card::before {
+    content: '';
+    position: absolute;
+    top: 0;
+    left: 0;
+    width: 100%;
+    height: 3px;
+    background: linear-gradient(to right, #861818, #ffd700);
+    transform: scaleX(0);
+    transform-origin: left;
+    transition: transform 0.3s ease;
+}
+
+.flight-card:hover {
+    transform: translateY(-2px);
+    background: rgba(255, 255, 255, 0.08);
+    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
+}
+
+.flight-card:hover::before {
+    transform: scaleX(1);
+}
+
+/* Flight Card Header */
+.flight-card-header {
+    display: flex;
+    justify-content: space-between;
+    align-items: center;
+    margin-bottom: 20px;
+}
+
+.airline-info {
+    display: flex;
+    align-items: center;
+    gap: 12px;
+}
+
+.airline-logo {
+    width: 40px;
+    height: 40px;
+    border-radius: 6px;
+    object-fit: contain;
+    background: rgba(255, 255, 255, 0.1);
+    padding: 4px;
+}
+
+.airline-name {
+    color: #f5f5f5;
+    font-weight: 600;
+    font-size: 16px;
+}
+
+.flight-price {
+    text-align: right;
+}
+
+.price-amount {
+    color: #ffd700;
+    font-size: 24px;
+    font-weight: bold;
+    display: block;
+}
+
+.price-type {
+    color: rgba(245, 245, 245, 0.6);
+    font-size: 12px;
+    text-transform: uppercase;
+    letter-spacing: 1px;
+}
+
+/* Flight Details */
+.flight-details {
+    margin-bottom: 20px;
+}
+
+.flight-route {
+    display: grid;
+    grid-template-columns: 1fr auto 1fr;
+    align-items: center;
+    gap: 20px;
+}
+
+.departure, .arrival {
+    text-align: center;
+}
+
+.departure {
+    text-align: left;
+}
+
+.arrival {
+    text-align: right;
+}
+
+.departure .time, .arrival .time {
+    color: #f5f5f5;
+    font-size: 20px;
+    font-weight: bold;
+    margin-bottom: 4px;
+}
+
+.departure .airport, .arrival .airport {
+    color: #ffd700;
+    font-size: 16px;
+    font-weight: 600;
+    margin-bottom: 2px;
+}
+
+.departure .city, .arrival .city {
+    color: rgba(245, 245, 245, 0.7);
+    font-size: 14px;
+}
+
+/* Flight Path */
+.flight-path {
+    display: flex;
+    flex-direction: column;
+    align-items: center;
+    gap: 8px;
+    min-width: 120px;
+}
+
+.duration {
+    color: #f5f5f5;
+    font-size: 14px;
+    font-weight: 500;
+}
+
+.route-line {
+    position: relative;
+    width: 100px;
+    height: 2px;
+    display: flex;
+    align-items: center;
+    justify-content: center;
+}
+
+.route-line .line {
+    width: 100%;
+    height: 2px;
+    background: linear-gradient(to right, #ffd700, #861818);
+    border-radius: 1px;
+}
+
+.route-line .fa-plane {
+    position: absolute;
+    color: #ffd700;
+    font-size: 16px;
+    background: #262626;
+    padding: 2px;
+}
+
+.stops {
+    font-size: 12px;
+    text-transform: uppercase;
+    letter-spacing: 1px;
+    font-weight: 500;
+}
+
+.stops.nonstop {
+    color: #4ade80;
+}
+
+.stops.one-stop {
+    color: #fbbf24;
+}
+
+.stops.multi-stop {
+    color: #f87171;
+}
+
+/* Flight Actions */
+.flight-actions {
+    display: flex;
+    gap: 12px;
+    justify-content: flex-end;
+}
+
+.add-reservation-btn, .book-btn {
+    padding: 10px 20px;
+    border: none;
+    border-radius: 6px;
+    font-weight: 600;
+    font-size: 14px;
+    cursor: pointer;
+    transition: all 0.3s ease;
+    display: flex;
+    align-items: center;
+    gap: 6px;
+}
+
+.add-reservation-btn {
+    background-color: rgba(79, 70, 229, 0.8);
+    color: white;
+    border: 1px solid rgba(79, 70, 229, 0.3);
+}
+
+.add-reservation-btn:hover {
+    background-color: rgba(79, 70, 229, 1);
+    transform: translateY(-1px);
+}
+
+.add-reservation-btn.added {
+    background-color: rgba(16, 185, 129, 0.8);
+    border-color: rgba(16, 185, 129, 0.3);
+    cursor: default;
+}
+
+.book-btn {
+    background-color: #ffd700;
+    color: #262626;
+    border: 1px solid rgba(255, 215, 0, 0.3);
+}
+
+.book-btn:hover {
+    background-color: #e6c200;
+    transform: translateY(-1px);
+    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
+}
+
+/* Mobile Responsive */
+@media (max-width: 768px) {
+    .flight-card {
+        padding: 15px;
+    }
+    
+    .flight-card-header {
+        flex-direction: column;
+        align-items: flex-start;
+        gap: 15px;
+    }
+    
+    .flight-price {
+        text-align: left;
+    }
+    
+    .flight-route {
+        grid-template-columns: 1fr;
+        gap: 15px;
+        text-align: center;
+    }
+    
+    .departure, .arrival {
+        text-align: center;
+    }
+    
+    .flight-actions {
+        flex-direction: column;
+        gap: 8px;
+    }
+    
+    .add-reservation-btn, .book-btn {
+        width: 100%;
+        justify-content: center;
+    }
+}
+
+@media (max-width: 480px) {
+    .flight-card {
+        padding: 12px;
+    }
+    
+    .airline-info {
+        gap: 8px;
+    }
+    
+    .airline-logo {
+        width: 32px;
+        height: 32px;
+    }
+    
+    .airline-name {
+        font-size: 14px;
+    }
+    
+    .price-amount {
+        font-size: 20px;
+    }
+    
+    .departure .time, .arrival .time {
+        font-size: 18px;
+    }
+    
+    .departure .airport, .arrival .airport {
+        font-size: 14px;
+    }
+}
-- 
2.46.2.windows.1

