const { createClient } = require('@supabase/supabase-js');

exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS'
  };

  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  try {
    // Only allow POST requests
    if (event.httpMethod !== 'POST') {
      return {
        statusCode: 405,
        headers,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Get user_id from request body
    const { user_id, action = 'cleanup' } = JSON.parse(event.body);
    
    if (!user_id) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing user_id parameter' })
      };
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      console.error('Missing Supabase configuration');
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ error: 'Server configuration error' })
      };
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Handle different actions
    switch (action) {
      case 'find':
        // Find duplicate events without deleting them
        const { data: duplicates, error: findError } = await supabase.rpc('find_duplicate_events', {
          p_user_id: user_id
        });

        if (findError) {
          console.error('Error finding duplicate events:', findError);
          throw findError;
        }

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({
            message: 'Duplicate events found',
            duplicates: duplicates || [],
            total_duplicates: duplicates ? duplicates.length : 0
          })
        };

      case 'stats':
        // Get duplicate statistics
        const { data: stats, error: statsError } = await supabase.rpc('get_duplicate_event_stats', {
          p_user_id: user_id
        });

        if (statsError) {
          console.error('Error getting duplicate stats:', statsError);
          throw statsError;
        }

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({
            message: 'Duplicate statistics retrieved',
            stats: stats
          })
        };

      case 'cleanup':
      default:
        // First, identify duplicate events
        const { data: duplicateEvents, error: findDuplicatesError } = await supabase.rpc('find_duplicate_events', {
          p_user_id: user_id
        });

        if (findDuplicatesError) {
          console.error('Error finding duplicate events:', findDuplicatesError);
          throw findDuplicatesError;
        }

        // If no duplicates found, return early
        if (!duplicateEvents || duplicateEvents.length === 0) {
          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
              message: 'No duplicate events found',
              duplicates_removed: 0,
              duplicates: []
            })
          };
        }

        // Delete the duplicate events, keeping the most recent one
        const { data: deletedCount, error: deleteError } = await supabase.rpc('delete_duplicate_events', {
          p_user_id: user_id
        });

        if (deleteError) {
          console.error('Error deleting duplicate events:', deleteError);
          throw deleteError;
        }

        return {
          statusCode: 200,
          headers,
          body: JSON.stringify({
            message: 'Duplicate events cleaned up successfully',
            duplicates_removed: deletedCount || 0,
            duplicates_found: duplicateEvents,
            total_duplicates_found: duplicateEvents.length
          })
        };
    }

  } catch (error) {
    console.error('Error in cleanup-event-duplicates function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Failed to process event duplicates',
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      })
    };
  }
};
