// Feedback System JavaScript - Server-side only
class FeedbackSystem {
    constructor() {
        this.modal = null;
        this.form = null;
        this.isModalLoaded = false;
        this.init();
    }

    async init() {
        // Load the modal HTML
        await this.loadModal();

        // Add feedback link to footer
        this.addFeedbackLink();

        // Bind events
        this.bindEvents();
    }

    async loadModal() {
        try {
            const response = await fetch('feedback-modal.html');
            const modalHTML = await response.text();

            // Create a temporary container to parse the HTML
            const temp = document.createElement('div');
            temp.innerHTML = modalHTML;

            // Append the modal to the body
            document.body.appendChild(temp.firstElementChild);

            // Get references to modal elements
            this.modal = document.getElementById('feedback-modal');
            this.form = document.getElementById('feedback-form');

            this.isModalLoaded = true;
        } catch (error) {
            console.error('Failed to load feedback modal:', error);
        }
    }

    addFeedbackLink() {
        // Wait for footer to be loaded
        const checkFooter = setInterval(() => {
            const footer = document.getElementById('footer');
            if (footer) {
                clearInterval(checkFooter);

                // Find the footer bottom section
                const footerBottom = footer.querySelector('.footer-bottom');
                if (footerBottom) {
                    // Create feedback link
                    const feedbackLink = document.createElement('a');
                    feedbackLink.href = '#';
                    feedbackLink.id = 'feedback-link';
                    feedbackLink.innerHTML = '<i class="fa fa-comment"></i> Problems & Suggestions';
                    feedbackLink.style.cssText = `
                        color: #6b7280;
                        text-decoration: none;
                        font-size: 0.875rem;
                        transition: color 0.2s ease;
                        margin-left: 20px;
                    `;

                    // Add hover effect
                    feedbackLink.addEventListener('mouseenter', () => {
                        feedbackLink.style.color = '#3b82f6';
                    });

                    feedbackLink.addEventListener('mouseleave', () => {
                        feedbackLink.style.color = '#6b7280';
                    });

                    // Add click event
                    feedbackLink.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.openModal();
                    });

                    // Insert before the back-to-top link
                    const backToTop = footerBottom.querySelector('#back-to-top');
                    if (backToTop) {
                        footerBottom.insertBefore(feedbackLink, backToTop);
                    } else {
                        footerBottom.appendChild(feedbackLink);
                    }
                }
            }
        }, 100);
    }

    bindEvents() {
        // Wait for modal to be loaded
        const checkModal = setInterval(() => {
            if (this.isModalLoaded && this.modal) {
                clearInterval(checkModal);

                // Close button events
                const closeButtons = this.modal.querySelectorAll('.feedback-close, .feedback-cancel, .feedback-close-success');
                closeButtons.forEach(button => {
                    button.addEventListener('click', () => this.closeModal());
                });

                // Form submission
                this.form.addEventListener('submit', (e) => this.handleSubmit(e));

                // Try again button
                const tryAgainBtn = this.modal.querySelector('.feedback-try-again');
                if (tryAgainBtn) {
                    tryAgainBtn.addEventListener('click', () => this.showForm());
                }

                // Close modal when clicking outside
                this.modal.addEventListener('click', (e) => {
                    if (e.target === this.modal) {
                        this.closeModal();
                    }
                });

                // Close modal with Escape key
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                        this.closeModal();
                    }
                });
            }
        }, 100);
    }

    openModal() {
        if (!this.isModalLoaded) {
            console.error('Modal not loaded yet');
            return;
        }

        this.modal.classList.add('show');
        document.body.style.overflow = 'hidden';

        // Focus on first input
        setTimeout(() => {
            const firstInput = this.modal.querySelector('input, select, textarea');
            if (firstInput) {
                firstInput.focus();
            }
        }, 100);
    }

    closeModal() {
        this.modal.classList.remove('show');
        document.body.style.overflow = '';

        // Reset form after a delay
        setTimeout(() => {
            this.resetForm();
        }, 300);
    }

    resetForm() {
        this.form.reset();
        this.showForm();
    }

    showForm() {
        this.form.style.display = 'block';
        this.modal.querySelector('#feedback-success').style.display = 'none';
        this.modal.querySelector('#feedback-error').style.display = 'none';
    }

    showSuccess() {
        this.form.style.display = 'none';
        this.modal.querySelector('#feedback-success').style.display = 'block';
        this.modal.querySelector('#feedback-error').style.display = 'none';
    }

    showError(message) {
        this.form.style.display = 'none';
        this.modal.querySelector('#feedback-error').style.display = 'block';
        this.modal.querySelector('#feedback-success').style.display = 'none';

        const errorMessage = this.modal.querySelector('#error-message');
        if (errorMessage) {
            errorMessage.textContent = message || 'Something went wrong. Please try again.';
        }
    }

    async handleSubmit(e) {
        e.preventDefault();

        // Add loading state
        this.form.classList.add('loading');

        try {
            const formData = new FormData(this.form);
            const feedbackData = {
                feedback_type: formData.get('feedback_type'),
                subject: formData.get('subject'),
                message: formData.get('message'),
                name: formData.get('name') || null,
                email: formData.get('email') || null,
                page_url: window.location.href,
                user_agent: navigator.userAgent
            };

            // Prepare headers
            const headers = {
                'Content-Type': 'application/json',
            };

            // Add authorization header if user is logged in and supabase is available
            if (typeof supabase !== 'undefined' && supabase && supabase.auth) {
                try {
                    const { data: { session } } = await supabase.auth.getSession();
                    if (session?.access_token) {
                        headers['Authorization'] = `Bearer ${session.access_token}`;
                    }
                } catch (authError) {
                    console.warn('Could not get auth session:', authError);
                    // Continue without auth - feedback can be anonymous
                }
            }

            // Submit to Netlify function (server-side only)
            const response = await fetch('/.netlify/functions/submit-feedback', {
                method: 'POST',
                headers,
                body: JSON.stringify(feedbackData)
            });

            const result = await response.json();

            if (!response.ok) {
                const errorMessage = result.error || 'Failed to submit feedback';
                const details = result.details || result.message || '';
                const fullError = details ? `${errorMessage}: ${details}` : errorMessage;
                throw new Error(fullError);
            }

            // Show success message
            this.showSuccess();

        } catch (error) {
            console.error('Error submitting feedback:', error);
            this.showError('Failed to submit feedback. Please try again later.');
        } finally {
            // Remove loading state
            this.form.classList.remove('loading');
        }
    }
}

// Initialize feedback system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new FeedbackSystem();
}); 