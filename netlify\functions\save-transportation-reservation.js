// Netlify function for saving transportation reservations to Supabase
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
const supabase = createClient(supabaseUrl, supabaseKey);

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'Preflight request successful' })
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Parse request body
    const requestBody = JSON.parse(event.body);
    const { 
      user_id, 
      id, 
      type, 
      from, 
      to, 
      date, 
      time, 
      carrier, 
      reference, 
      notes, 
      tripType 
    } = requestBody;

    // Validate required parameters
    if (!user_id || !id || !type || !from || !to || !date) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameters: user_id, id, type, from, to, date' })
      };
    }

    // Insert reservation into Supabase
    const { data, error } = await supabase
      .from('transportation_reservations')
      .insert([
        {
          user_id,
          reservation_id: id,
          type,
          from_location: from,
          to_location: to,
          departure_date: date,
          departure_time: time || null,
          carrier: carrier || null,
          reference: reference || null,
          notes: notes || null,
          trip_type: tripType || 'one-way'
        }
      ])
      .select();

    if (error) {
      console.error('Error saving transportation reservation to Supabase:', error);
      
      // Check if the error is because the table doesn't exist
      if (error.code === '42P01') {
        console.log('Table transportation_reservations does not exist, creating it...');
        
        // Try to create the table directly with SQL
        const { error: sqlError } = await supabase.rpc('exec_sql', {
          sql_query: `
            CREATE TABLE IF NOT EXISTS transportation_reservations (
              id SERIAL PRIMARY KEY,
              user_id TEXT NOT NULL,
              reservation_id TEXT NOT NULL,
              type TEXT NOT NULL,
              from_location TEXT NOT NULL,
              to_location TEXT NOT NULL,
              departure_date DATE NOT NULL,
              departure_time TIME,
              carrier TEXT,
              reference TEXT,
              notes TEXT,
              trip_type TEXT DEFAULT 'one-way',
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            
            CREATE INDEX IF NOT EXISTS idx_transportation_reservations_user_id ON transportation_reservations(user_id);
            CREATE INDEX IF NOT EXISTS idx_transportation_reservations_reservation_id ON transportation_reservations(reservation_id);
            
            ALTER TABLE transportation_reservations ENABLE ROW LEVEL SECURITY;
            
            CREATE POLICY select_own_reservations ON transportation_reservations
              FOR SELECT USING (auth.uid()::text = user_id);
            
            CREATE POLICY insert_own_reservations ON transportation_reservations
              FOR INSERT WITH CHECK (auth.uid()::text = user_id);
            
            CREATE POLICY update_own_reservations ON transportation_reservations
              FOR UPDATE USING (auth.uid()::text = user_id);
            
            CREATE POLICY delete_own_reservations ON transportation_reservations
              FOR DELETE USING (auth.uid()::text = user_id);
          `
        });
        
        if (sqlError) {
          console.error('Error creating SQL function:', sqlError);
          
          // Fallback to direct insert if we can't create the function
          const { error: insertError } = await supabase
            .from('transportation_reservations')
            .insert([
              {
                user_id: 'system_init',
                reservation_id: 'system_init',
                type: 'system',
                from_location: 'System',
                to_location: 'System',
                departure_date: new Date().toISOString().split('T')[0],
                notes: 'This record was created to initialize the table'
              }
            ]);
            
          if (insertError) {
            console.error('Error creating table via insert:', insertError);
          }
        }
        
        // Try the insert again
        const { data: retryData, error: retryError } = await supabase
          .from('transportation_reservations')
          .insert([
            {
              user_id,
              reservation_id: id,
              type,
              from_location: from,
              to_location: to,
              departure_date: date,
              departure_time: time || null,
              carrier: carrier || null,
              reference: reference || null,
              notes: notes || null,
              trip_type: tripType || 'one-way'
            }
          ])
          .select();
          
        if (retryError) {
          console.error('Error saving transportation reservation after table creation:', retryError);
          return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Failed to save transportation reservation after table creation', message: retryError.message })
          };
        }
        
        return {
          statusCode: 201,
          headers,
          body: JSON.stringify({
            success: true,
            message: 'Transportation reservation saved successfully after creating table',
            data: retryData
          })
        };
      }
      
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({ error: 'Failed to save transportation reservation', message: error.message })
      };
    }
    
    return {
      statusCode: 201,
      headers,
      body: JSON.stringify({
        success: true,
        message: 'Transportation reservation saved successfully',
        data
      })
    };
  } catch (error) {
    console.error('Error in save-transportation-reservation function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        message: error.message
      })
    };
  }
};
