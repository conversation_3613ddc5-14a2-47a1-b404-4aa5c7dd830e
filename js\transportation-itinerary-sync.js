/**
 * Transportation-Itinerary Bidirectional Sync Module
 * 
 * This module handles the synchronization between transportation reservations
 * and itinerary events, ensuring changes in one are reflected in the other.
 */

console.log('Loading transportation-itinerary sync module...');

// Create global namespace
window.transportationItinerarySync = window.transportationItinerarySync || {};

/**
 * Check if an event is linked to a transportation reservation
 * @param {Object} event - The event to check
 * @returns {string|null} - The reservation ID if linked, null otherwise
 */
function getLinkedReservationId(event) {
    // Check for direct link
    if (event.transportationReservationId) {
        return event.transportationReservationId;
    }
    
    // Check for reservation ID in description
    if (event.description && event.description.includes('Reservation ID:')) {
        const match = event.description.match(/Reservation ID:\s*([^\n\r]+)/);
        if (match && match[1]) {
            return match[1].trim();
        }
    }
    
    // Check if it's a transportation category event
    if (event.category === 'transportation' && event.description) {
        // Try to extract reservation ID from description
        const lines = event.description.split('\n');
        for (const line of lines) {
            if (line.includes('Reservation ID:')) {
                const reservationId = line.split('Reservation ID:')[1]?.trim();
                if (reservationId) {
                    return reservationId;
                }
            }
        }
    }
    
    return null;
}

/**
 * Update linked transportation reservation when event is updated
 * @param {Object} updatedEvent - The updated event
 */
async function updateLinkedReservation(updatedEvent) {
    try {
        const reservationId = getLinkedReservationId(updatedEvent);
        if (!reservationId) {
            console.log('Event is not linked to a transportation reservation');
            return;
        }

        console.log('Updating linked transportation reservation:', reservationId);

        // Get current reservations
        const reservations = JSON.parse(localStorage.getItem('transportationReservations') || '[]');
        const reservationIndex = reservations.findIndex(r => r.id === reservationId);

        if (reservationIndex === -1) {
            console.log('Linked reservation not found in localStorage');
            return;
        }

        // Extract data from the updated event
        const reservation = reservations[reservationIndex];
        
        // Parse the event data to update reservation
        // Extract date and time from event start
        const eventStart = new Date(updatedEvent.start);
        const newDate = eventStart.toISOString().split('T')[0];
        const newTime = updatedEvent.start.includes('T') ? 
            eventStart.toTimeString().split(' ')[0].substring(0, 5) : 
            reservation.time || '00:00';

        // Extract carrier and reference from description if available
        let carrier = reservation.carrier;
        let reference = reservation.reference;
        let notes = reservation.notes;

        if (updatedEvent.description) {
            const lines = updatedEvent.description.split('\n');
            for (const line of lines) {
                if (line.includes('Carrier:')) {
                    const carrierMatch = line.split('Carrier:')[1]?.trim();
                    if (carrierMatch && carrierMatch !== 'N/A') {
                        carrier = carrierMatch;
                    }
                } else if (line.includes('Reference:')) {
                    const refMatch = line.split('Reference:')[1]?.trim();
                    if (refMatch && refMatch !== 'N/A') {
                        reference = refMatch;
                    }
                } else if (!line.includes('Reservation ID:') && !line.includes('Carrier:') && !line.includes('Reference:')) {
                    // This might be notes
                    if (line.trim() && line.trim() !== reservation.notes) {
                        notes = line.trim();
                    }
                }
            }
        }

        // Extract from/to from location if changed
        let from = reservation.from;
        let to = reservation.to;
        
        if (updatedEvent.location && updatedEvent.location.includes('From:') && updatedEvent.location.includes('To:')) {
            const locationMatch = updatedEvent.location.match(/From:\s*([^T]+)\s*To:\s*(.+)/);
            if (locationMatch) {
                from = locationMatch[1].trim();
                to = locationMatch[2].trim();
            }
        }

        // Update the reservation
        const updatedReservation = {
            ...reservation,
            date: newDate,
            time: newTime,
            from: from,
            to: to,
            carrier: carrier,
            reference: reference,
            notes: notes
        };

        // Update in localStorage
        reservations[reservationIndex] = updatedReservation;
        localStorage.setItem('transportationReservations', JSON.stringify(reservations));

        // Update in Supabase if user is logged in
        if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
            try {
                const supabaseReservation = { ...updatedReservation, user_id: auth.user.id };
                await window.itineraryAPI.saveTransportationReservation(supabaseReservation);
                console.log('Updated linked reservation in Supabase');
            } catch (error) {
                console.error('Error updating linked reservation in Supabase:', error);
            }
        }

        console.log('Successfully updated linked transportation reservation');

        // Refresh transportation page if it's open
        if (window.transportationReservations && typeof window.transportationReservations.loadReservations === 'function') {
            window.transportationReservations.loadReservations();
        }

    } catch (error) {
        console.error('Error updating linked transportation reservation:', error);
    }
}

/**
 * Delete linked transportation reservation when event is deleted
 * @param {string} eventId - The ID of the deleted event
 */
async function deleteLinkedReservation(eventId) {
    try {
        console.log('Checking for linked reservation to delete for event:', eventId);

        // First, try to find the event in localStorage to get the reservation ID
        const events = JSON.parse(localStorage.getItem('events') || '[]');
        const deletedEvent = events.find(e => e.id === eventId);
        
        let reservationId = null;
        if (deletedEvent) {
            reservationId = getLinkedReservationId(deletedEvent);
        }

        // If we couldn't find it in current events, check if it was passed in the event data
        if (!reservationId) {
            console.log('Could not find linked reservation ID for deleted event');
            return;
        }

        console.log('Deleting linked transportation reservation:', reservationId);

        // Get current reservations
        let reservations = JSON.parse(localStorage.getItem('transportationReservations') || '[]');
        const originalLength = reservations.length;
        
        // Remove the linked reservation
        reservations = reservations.filter(r => r.id !== reservationId);
        
        if (reservations.length === originalLength) {
            console.log('Linked reservation not found in localStorage');
            return;
        }

        // Update localStorage
        localStorage.setItem('transportationReservations', JSON.stringify(reservations));

        // Delete from Supabase if user is logged in
        if (typeof auth !== 'undefined' && auth.isLoggedIn && auth.isLoggedIn() && auth.user) {
            try {
                await window.itineraryAPI.deleteTransportationReservation(auth.user.id, reservationId);
                console.log('Deleted linked reservation from Supabase');
            } catch (error) {
                console.error('Error deleting linked reservation from Supabase:', error);
            }
        }

        console.log('Successfully deleted linked transportation reservation');

        // Refresh transportation page if it's open
        if (window.transportationReservations && typeof window.transportationReservations.loadReservations === 'function') {
            window.transportationReservations.loadReservations();
        }

    } catch (error) {
        console.error('Error deleting linked transportation reservation:', error);
    }
}

/**
 * Hook into the itinerary sync module to add reverse sync functionality
 */
function initializeReverseSync() {
    // Override the itinerary sync module's update and delete functions
    if (window.itinerarySync) {
        // Store original functions
        const originalUpdateEvent = window.itinerarySync.updateEvent;
        const originalDeleteEvent = window.itinerarySync.deleteEvent;

        // Override update function
        if (originalUpdateEvent) {
            window.itinerarySync.updateEvent = async function(updatedEvent) {
                console.log('Intercepting event update for reverse sync:', updatedEvent.id);
                
                // Call original update function
                const result = await originalUpdateEvent.call(this, updatedEvent);
                
                // Perform reverse sync
                await updateLinkedReservation(updatedEvent);
                
                return result;
            };
        }

        // Override delete function
        if (originalDeleteEvent) {
            window.itinerarySync.deleteEvent = async function(eventId) {
                console.log('Intercepting event deletion for reverse sync:', eventId);
                
                // Perform reverse sync before deletion (while we can still access the event data)
                await deleteLinkedReservation(eventId);
                
                // Call original delete function
                const result = await originalDeleteEvent.call(this, eventId);
                
                return result;
            };
        }

        console.log('Reverse sync hooks installed successfully');
    } else {
        console.warn('itinerarySync module not found, reverse sync hooks not installed');
    }
}

// Export functions
window.transportationItinerarySync = {
    updateLinkedReservation,
    deleteLinkedReservation,
    getLinkedReservationId,
    initializeReverseSync
};

// Auto-initialize when the module loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for other modules to load
    setTimeout(initializeReverseSync, 1000);
});

console.log('Transportation-itinerary sync module loaded successfully');
