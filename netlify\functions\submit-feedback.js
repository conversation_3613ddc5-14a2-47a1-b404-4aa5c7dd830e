const { createClient } = require('@supabase/supabase-js');

// Rate limiting - simple in-memory store (use Redis in production)
const rateLimitStore = new Map();

// Rate limiting function
function checkRateLimit(identifier, limit = 5, windowMs = 60000) {
    const now = Date.now();
    const windowStart = now - windowMs;

    if (!rateLimitStore.has(identifier)) {
        rateLimitStore.set(identifier, []);
    }

    const requests = rateLimitStore.get(identifier);

    // Remove old requests outside the window
    const validRequests = requests.filter(timestamp => timestamp > windowStart);
    rateLimitStore.set(identifier, validRequests);

    if (validRequests.length >= limit) {
        return false; // Rate limit exceeded
    }

    validRequests.push(now);
    return true; // Rate limit OK
}

// Input validation
function validateFeedback(data) {
    const errors = [];

    // Required fields
    if (!data.feedback_type || !['suggestion', 'bug_report', 'general_feedback'].includes(data.feedback_type)) {
        errors.push('Invalid feedback type');
    }

    if (!data.subject || data.subject.trim().length < 3 || data.subject.trim().length > 200) {
        errors.push('Subject must be between 3 and 200 characters');
    }

    if (!data.message || data.message.trim().length > 5000) {
        errors.push('Message must be less than 5000 characters');
    }

    // Optional fields validation
    if (data.name && (data.name.trim().length < 2 || data.name.trim().length > 100)) {
        errors.push('Name must be between 2 and 100 characters');
    }

    if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
        errors.push('Invalid email format');
    }

    return errors;
}

// Sanitize input
function sanitizeInput(input) {
    if (typeof input !== 'string') return input;

    // Remove potentially dangerous characters and limit length
    return input
        .trim()
        .replace(/[<>]/g, '') // Remove < and > to prevent HTML injection
        .substring(0, 5000); // Limit length
}

exports.handler = async (event, context) => {
    // Enable CORS - same as other functions
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Content-Type': 'application/json'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // Only allow POST requests
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // Rate limiting based on IP address
        const clientIP = event.headers['client-ip'] ||
            event.headers['x-forwarded-for'] ||
            event.headers['x-real-ip'] ||
            'unknown';

        if (!checkRateLimit(clientIP, 5, 60000)) { // 5 requests per minute
            return {
                statusCode: 429,
                headers,
                body: JSON.stringify({ error: 'Too many requests. Please try again later.' })
            };
        }

        // Parse and validate the request body
        let feedbackData;
        try {
            feedbackData = JSON.parse(event.body);
        } catch (parseError) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Invalid JSON in request body' })
            };
        }

        // Validate input
        const validationErrors = validateFeedback(feedbackData);
        if (validationErrors.length > 0) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: validationErrors.join(', ') })
            };
        }

        // Sanitize inputs
        const sanitizedData = {
            feedback_type: feedbackData.feedback_type,
            subject: sanitizeInput(feedbackData.subject),
            message: sanitizeInput(feedbackData.message),
            name: feedbackData.name ? sanitizeInput(feedbackData.name) : null,
            email: feedbackData.email ? sanitizeInput(feedbackData.email) : null,
            page_url: feedbackData.page_url ? sanitizeInput(feedbackData.page_url) : null,
            user_agent: feedbackData.user_agent ? sanitizeInput(feedbackData.user_agent) : null
        };

        // Initialize Supabase client with same env vars as other functions
        const supabaseUrl = process.env.SUPABASE_URL || 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
        const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';

        console.log('Supabase configuration:', {
            url: supabaseUrl ? 'Set' : 'Not set',
            key: supabaseKey ? 'Set' : 'Not set',
            keyLength: supabaseKey ? supabaseKey.length : 0
        });

        if (!supabaseUrl || !supabaseKey) {
            console.error('Missing Supabase configuration');
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({ error: 'Server configuration error' })
            };
        }

        const supabase = createClient(supabaseUrl, supabaseKey);

        // Handle user authentication server-side
        let userId = null;
        const authHeader = event.headers.authorization;

        if (authHeader && authHeader.startsWith('Bearer ')) {
            try {
                const token = authHeader.substring(7);
                const { data: { user }, error: authError } = await supabase.auth.getUser(token);

                if (!authError && user) {
                    userId = user.id;
                }
            } catch (authError) {
                console.warn('Authentication error:', authError);
                // Continue without user ID - feedback can be anonymous
            }
        }

        // Prepare the feedback data
        const feedback = {
            ...sanitizedData,
            user_id: userId,
            created_at: new Date().toISOString()
        };

        // Insert the feedback into the database
        console.log('Attempting to insert feedback:', {
            feedback_type: feedback.feedback_type,
            subject: feedback.subject,
            hasUser: !!userId,
            timestamp: new Date().toISOString()
        });

        const { data, error } = await supabase
            .from('feedback')
            .insert([feedback])
            .select('id')
            .single();

        if (error) {
            console.error('Supabase error details:', {
                code: error.code,
                message: error.message,
                details: error.details,
                hint: error.hint
            });

            // For now, return a more specific error message
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify({
                    error: 'Failed to save feedback',
                    details: error.message,
                    code: error.code
                })
            };
        }

        // Log successful submission (without sensitive data)
        console.log('Feedback submitted successfully:', {
            id: data.id,
            type: feedback.feedback_type,
            hasUser: !!userId,
            timestamp: new Date().toISOString()
        });

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Feedback submitted successfully',
                id: data.id
            })
        };

    } catch (error) {
        console.error('Function error:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Internal server error' })
        };
    }
}; 