// Netlify function for syncing user markers between local storage and Supabase
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
// Using service role key to bypass RLS policies
const supabaseUrl = process.env.SUPABASE_URL || 'https://vwpyvjoycnrgmpatzqzk.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ3cHl2am95Y25yZ21wYXR6cXprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDExMzk0MTksImV4cCI6MjA1NjcxNTQxOX0.sG7amfnd8UTMv3zzmaa3KKyPRTzW70cDW9VVcUCSq4c';
const supabase = createClient(supabaseUrl, supabaseKey);

// CORS headers
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Content-Type': 'application/json'
};

exports.handler = async (event, context) => {
  // Handle preflight OPTIONS request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'Preflight request successful' })
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    console.log('sync-user-markers function called');

    // Parse request body
    const requestBody = JSON.parse(event.body);
    const { user_id, action, markers } = requestBody;

    console.log(`Received request for user ${user_id} with action: ${action}`);

    // Validate required parameters
    if (!user_id) {
      console.error('Missing required parameter: user_id');
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameter: user_id' })
      };
    }

    // Handle fetch action
    if (action === 'fetch') {
      console.log(`Fetching markers for user ${user_id}`);
      
      // Get existing markers for this user from Supabase
      const { data: existingMarkers, error: fetchError } = await supabase
        .from('user_markers')
        .select('*')
        .eq('user_id', user_id)
        .order('created_at', { ascending: false });

      if (fetchError) {
        console.error('Error fetching existing markers:', fetchError);
        
        // Check if the error is because the table doesn't exist
        if (fetchError.code === '42P01') {
          console.log('Table user_markers does not exist, returning empty array');
          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ markers: [] })
          };
        }
        
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({ error: 'Failed to fetch markers', message: fetchError.message })
        };
      }

      console.log(`Found ${existingMarkers?.length || 0} markers for user ${user_id}`);
      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({ markers: existingMarkers || [] })
      };
    }

    // Handle sync action (requires markers array)
    if (!markers || !Array.isArray(markers)) {
      console.error('Missing required parameter for sync: markers array');
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameter: markers array' })
      };
    }

    console.log(`Received sync request for user ${user_id} with ${markers.length} markers`);
    if (markers.length > 0) {
      console.log('First marker sample:', JSON.stringify(markers[0]));
    }

    // Get existing markers for this user from Supabase
    const { data: existingMarkers, error: fetchError } = await supabase
      .from('user_markers')
      .select('*')
      .eq('user_id', user_id);

    if (fetchError) {
      console.error('Error fetching existing markers:', fetchError);
      
      // Check if the error is because the table doesn't exist
      if (fetchError.code === '42P01') {
        console.log('Table user_markers does not exist, attempting to create it');
        
        // Try to create the table directly with SQL
        const { error: sqlError } = await supabase.rpc('exec_sql', {
          sql_query: `
            CREATE TABLE IF NOT EXISTS user_markers (
              id SERIAL PRIMARY KEY,
              user_id TEXT NOT NULL,
              lat FLOAT NOT NULL,
              lng FLOAT NOT NULL,
              name TEXT NOT NULL,
              description TEXT,
              placeName TEXT,
              isPOI BOOLEAN DEFAULT FALSE,
              isCustom BOOLEAN DEFAULT FALSE,
              wikiName TEXT,
              addedToItinerary BOOLEAN DEFAULT FALSE,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            
            CREATE INDEX IF NOT EXISTS idx_user_markers_user_id ON user_markers(user_id);
            CREATE INDEX IF NOT EXISTS idx_user_markers_location ON user_markers(lat, lng);
          `
        });

        if (sqlError) {
          console.error('Error creating SQL function:', sqlError);
          return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Failed to create user_markers table', message: sqlError.message })
          };
        }
      } else {
        return {
          statusCode: 500,
          headers,
          body: JSON.stringify({ error: 'Failed to fetch existing markers', message: fetchError.message })
        };
      }
    }

    // Create a map of existing markers by location and name for quick lookup
    const existingMarkersMap = {};
    if (existingMarkers && existingMarkers.length > 0) {
      console.log(`Found ${existingMarkers.length} existing markers for user ${user_id}`);
      existingMarkers.forEach(marker => {
        const key = `${marker.lat}_${marker.lng}_${marker.name}`;
        existingMarkersMap[key] = marker;
      });
    } else {
      console.log(`No existing markers found for user ${user_id}`);
    }

    // Process each marker from the client
    console.log(`Processing ${markers.length} markers from client`);
    const results = [];
    for (const marker of markers) {
      console.log(`Processing marker: ${marker.name} at ${marker.lat}, ${marker.lng}`);

      // Ensure each marker has the user_id
      marker.user_id = user_id;

      // Convert marker properties to database format
      const dbMarker = {
        user_id: marker.user_id,
        lat: parseFloat(marker.lat),
        lng: parseFloat(marker.lng),
        name: marker.name,
        description: marker.description || null,
        placeName: marker.placeName || null,
        isPOI: marker.isPOI || false,
        isCustom: marker.isCustom || false,
        wikiName: marker.wikiName || null,
        addedToItinerary: marker.addedToItinerary || false
      };

      console.log('Converted to DB format:', dbMarker);

      // Check if this marker already exists
      const markerKey = `${dbMarker.lat}_${dbMarker.lng}_${dbMarker.name}`;
      if (existingMarkersMap[markerKey]) {
        console.log(`Marker ${marker.name} exists, updating...`);
        // Update existing marker
        const { data, error } = await supabase
          .from('user_markers')
          .update(dbMarker)
          .eq('user_id', user_id)
          .eq('lat', dbMarker.lat)
          .eq('lng', dbMarker.lng)
          .eq('name', dbMarker.name)
          .select();

        if (error) {
          console.error('Error updating marker:', error);
          results.push({ error: error.message, marker });
        } else {
          console.log(`Marker ${marker.name} updated successfully`);
          results.push({ success: true, marker: data[0], action: 'updated' });
        }
      } else {
        console.log(`Marker ${marker.name} is new, inserting...`);
        // Insert new marker
        const { data, error } = await supabase
          .from('user_markers')
          .insert([dbMarker])
          .select();

        if (error) {
          console.error('Error inserting marker:', error);
          results.push({ error: error.message, marker });
        } else {
          console.log(`Marker ${marker.name} inserted successfully`);
          results.push({ success: true, marker: data[0], action: 'inserted' });
        }
      }
    }

    // Log summary of operations
    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => r.error).length;
    console.log(`Sync completed with ${successCount} successful operations and ${errorCount} errors`);

    // Return successful response with results
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ success: true, message: 'Markers synced successfully', results })
    };

  } catch (error) {
    console.error('Error in sync-user-markers function:', error);

    // Return error response
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Failed to sync markers',
        message: error.message
      })
    };
  }
};
