# Event Cleanup and Deduplication System

This document describes the comprehensive event cleanup and deduplication system for Vestigia, designed to handle duplicate events based on user ID and event content.

## Overview

The system provides both client-side and server-side deduplication capabilities:

- **Local Deduplication**: JavaScript functions for cleaning up events in local storage
- **Remote Deduplication**: SQL functions and API endpoints for cleaning up events in Supabase
- **Comprehensive Cleanup**: Combined approach that handles both local and remote duplicates

## Components

### 1. SQL Functions (`database/event_duplicate_cleanup_functions.sql`)

#### `find_duplicate_events(p_user_id TEXT)`
Finds duplicate events for a specific user without deleting them.

**Returns**: Table with duplicate event details including:
- `id`, `event_id`, `title`, `start_time`, `end_time`, `location`, `created_at`, `duplicate_count`

#### `delete_duplicate_events(p_user_id TEXT)`
Deletes duplicate events for a user, keeping the most recent version.

**Returns**: Integer count of deleted events

#### `cleanup_all_duplicate_events()`
Cleans up duplicate events for all users (admin function).

**Returns**: JSONB with cleanup statistics by user

#### `get_duplicate_event_stats(p_user_id TEXT)`
Returns statistics about duplicate events for a user.

**Returns**: JSONB with total events, duplicate groups, and unique events count

### 2. API Endpoint (`netlify/functions/cleanup-event-duplicates.js`)

**Endpoint**: `POST /api/cleanup-event-duplicates`

**Actions**:
- `find`: Find duplicates without deleting
- `stats`: Get duplicate statistics
- `cleanup`: Clean up duplicates (default)

**Request Body**:
```json
{
  "user_id": "string",
  "action": "find|stats|cleanup"
}
```

### 3. JavaScript API Client (`js/itinerary-api-client.js`)

Added methods to the existing `window.itineraryAPI` object:

#### `findDuplicateEvents(userId)`
Find duplicate events for a user.

#### `getDuplicateEventStats(userId)`
Get duplicate statistics for a user.

#### `cleanupDuplicateEvents(userId)`
Clean up duplicate events for a user.

### 4. Utility Functions (`js/event-cleanup-utils.js`)

Provides comprehensive client-side cleanup utilities:

#### `deduplicateEventsLocally(events, userId)`
Removes duplicate events from a local array.

#### `areEventsDuplicates(event1, event2)`
Checks if two events are duplicates based on content.

#### `comprehensiveEventCleanup(userId, localEvents, syncWithSupabase)`
Runs complete cleanup for both local and remote events.

#### `validateEventForDuplicates(newEvent, existingEvents)`
Validates new events before saving to prevent duplicates.

## Duplicate Detection Criteria

Events are considered duplicates if they have the same:

1. **User ID** - Must belong to the same user
2. **Title** - Case-insensitive, trimmed comparison
3. **Start Time** - Exact timestamp match
4. **End Time** - Exact timestamp match (or start time if end time is null)
5. **Location** - Case-insensitive, trimmed comparison (empty string if null)
6. **Description** - Case-insensitive, trimmed comparison (empty string if null)
7. **Category** - Case-insensitive, trimmed comparison (empty string if null)

## Priority System

When duplicates are found, the system keeps the most recent event based on:

1. `updated_at` timestamp (most recent first)
2. `created_at` timestamp (most recent first)
3. `id` (highest first)

## Usage Examples

### 1. Basic Cleanup via API

```javascript
// Find duplicates
const duplicates = await window.itineraryAPI.findDuplicateEvents('user-123');
console.log(`Found ${duplicates.total_duplicates} duplicates`);

// Get statistics
const stats = await window.itineraryAPI.getDuplicateEventStats('user-123');
console.log(`Total events: ${stats.stats.total_events}`);

// Clean up duplicates
const result = await window.itineraryAPI.cleanupDuplicateEvents('user-123');
console.log(`Removed ${result.duplicates_removed} duplicates`);
```

### 2. Local Deduplication

```javascript
// Load events from local storage
const events = JSON.parse(localStorage.getItem('itineraryEvents') || '[]');

// Deduplicate locally
const cleanEvents = window.eventCleanupUtils.deduplicateEventsLocally(events, 'user-123');

// Save back to local storage
localStorage.setItem('itineraryEvents', JSON.stringify(cleanEvents));
```

### 3. Comprehensive Cleanup

```javascript
// Run complete cleanup (local + remote)
const localEvents = JSON.parse(localStorage.getItem('itineraryEvents') || '[]');
const result = await window.eventCleanupUtils.comprehensiveEventCleanup(
  'user-123', 
  localEvents, 
  true // sync with Supabase
);

// Save cleaned events
localStorage.setItem('itineraryEvents', JSON.stringify(result.cleanedEvents));
console.log(`Total duplicates removed: ${result.totalRemoved}`);
```

### 4. Validation Before Saving

```javascript
// Validate new event before saving
const newEvent = { /* event data */ };
const existingEvents = JSON.parse(localStorage.getItem('itineraryEvents') || '[]');

const validation = window.eventCleanupUtils.validateEventForDuplicates(newEvent, existingEvents);

if (validation.isDuplicate) {
  console.log('Duplicate detected:', validation.reason);
  // Handle duplicate (update existing or skip)
} else {
  // Safe to save new event
  existingEvents.push(newEvent);
  localStorage.setItem('itineraryEvents', JSON.stringify(existingEvents));
}
```

## Database Setup

1. Run the SQL script to create the cleanup functions:
```sql
-- Run in Supabase SQL Editor
\i database/event_duplicate_cleanup_functions.sql
```

2. Test the functions:
```sql
-- Test duplicate detection
SELECT test_event_duplicate_detection();

-- Find duplicates for a user
SELECT * FROM find_duplicate_events('user-123');

-- Get statistics
SELECT get_duplicate_event_stats('user-123');
```

## Integration with Existing Code

### In `itinerary.html`
Add the cleanup utilities script:
```html
<script src="js/event-cleanup-utils.js"></script>
```

### In Event Sync Functions
Integrate cleanup into existing sync logic:
```javascript
// Before syncing, clean up local duplicates
const cleanEvents = window.eventCleanupUtils.deduplicateEventsLocally(events, userId);

// After syncing, optionally clean up remote duplicates
await window.itineraryAPI.cleanupDuplicateEvents(userId);
```

## Testing

Use the test page `test-event-cleanup.html` to:

1. Create test events with duplicates
2. Test local deduplication
3. Test remote cleanup via API
4. Run comprehensive cleanup
5. View statistics and results

## Security Considerations

- All API endpoints use CORS headers for cross-origin requests
- User ID validation is performed on all requests
- RLS (Row Level Security) policies ensure users can only access their own events
- Service key is used for server-side operations

## Performance Notes

- Local deduplication is fast and runs in-memory
- Remote cleanup uses database indexes for efficient queries
- Batch operations are used for multiple event processing
- Statistics queries are optimized with proper indexing

## Error Handling

The system includes comprehensive error handling:

- API errors are caught and returned with appropriate status codes
- Client-side errors are logged and don't break the application
- Partial failures in comprehensive cleanup are reported but don't stop the process
- Validation errors provide helpful suggestions for resolution
